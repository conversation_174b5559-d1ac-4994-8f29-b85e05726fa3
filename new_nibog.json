{"name": "new-nibog", "nodes": [{"parameters": {"httpMethod": "POST", "path": "v1/nibog/role/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1940, 300], "id": "6dcf16d5-6089-4f11-851c-452c7d288c6d", "name": "Role Create", "webhookId": "f68e500c-0e7b-4b5f-8fac-dbfc294deec7"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "roles", "mode": "list", "cachedResultName": "roles"}, "columns": {"mappingMode": "defineBelow", "value": {"name": "={{ $json.body.name }}", "description": "={{ $json.body.description }}", "id": 0}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1760, 300], "id": "725a4325-655d-4de6-8347-bc6b6166b63b", "name": "Role Table", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/role/list", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1940, 480], "id": "9870e0eb-e8ae-4986-bf0a-218ea022dc4c", "name": "List Of roles", "webhookId": "f68e500c-0e7b-4b5f-8fac-dbfc294deec7"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM roles\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1760, 480], "id": "ff13172c-7901-438e-9961-aa390b6c2699", "name": "Role-table-get-list", "alwaysOutputData": false, "notesInFlow": false, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/role/get", "responseMode": "lastNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1520, 300], "id": "b547e5bd-34ef-4455-883d-c6e49af68bf5", "name": "Get One Role", "webhookId": "f68e500c-0e7b-4b5f-8fac-dbfc294deec7"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/role/update", "responseMode": "lastNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1520, 480], "id": "238e2ec1-3f27-4691-9a55-2e1585553ef7", "name": "Update a Role", "webhookId": "f68e500c-0e7b-4b5f-8fac-dbfc294deec7"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/role/delete", "responseMode": "lastNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1740, 700], "id": "450c8fae-f6c3-48d4-8693-d88923942e19", "name": "Delete a Role", "webhookId": "f68e500c-0e7b-4b5f-8fac-dbfc294deec7"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "roles", "mode": "list", "cachedResultName": "roles"}, "returnAll": true, "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1340, 300], "id": "d4589831-7548-4d2a-a3a2-e22a95755057", "name": "Role Table get single role", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "roles", "mode": "list", "cachedResultName": "roles"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.body.id }}", "description": "={{ $json.body.description }}", "name": "={{ $json.body.name }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1340, 480], "id": "ba4bf913-bf83-4598-a9c9-350247acdb86", "name": "Role Table update single role", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "roles", "mode": "list", "cachedResultName": "roles"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1560, 700], "id": "de9cb3cd-d34d-432c-8717-434bb7860725", "name": "Role Table1", "notesInFlow": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## Role api's", "height": 680, "width": 940}, "type": "n8n-nodes-base.stickyNote", "position": [-2020, 200], "typeVersion": 1, "id": "ba06f9f3-ecdc-45fc-be73-b24c34e3560a", "name": "<PERSON><PERSON>"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/city/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1020, 300], "id": "7bebdfc7-6ec8-4ffd-9731-da436d86a53c", "name": "City add", "webhookId": "aaf9d82a-4d16-4f77-9dc9-7d0a75e59c6d"}, {"parameters": {"path": "v1/nibog/city/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1040, 460], "id": "26f0aae8-53e6-4a9d-8f79-d860ac0b20e7", "name": "get all city", "webhookId": "aaf9d82a-4d16-4f77-9dc9-7d0a75e59c6d"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/city/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-720, 300], "id": "fc662446-91f7-4a15-a03b-dbbb4e80787e", "name": "update single city", "webhookId": "aaf9d82a-4d16-4f77-9dc9-7d0a75e59c6d"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/city/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-720, 460], "id": "84c69c4c-f209-48f8-8375-a1c1152ad8e0", "name": "single city delete", "webhookId": "aaf9d82a-4d16-4f77-9dc9-7d0a75e59c6d"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/city/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1020, 640], "id": "c1d79090-935e-4b90-843d-d737649c1cdc", "name": "get single city by id", "webhookId": "aaf9d82a-4d16-4f77-9dc9-7d0a75e59c6d"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "cities", "mode": "list", "cachedResultName": "cities"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "city_name": "={{ $json.body.city_name }}", "state": "={{ $json.body.state }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "city_name", "displayName": "city_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "state", "displayName": "state", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-920, 300], "id": "b83c13f6-39a7-4432-9dbb-dccab4a824c8", "name": "Add city", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "cities", "mode": "list", "cachedResultName": "cities"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "id": "={{ $json.body.id }}", "city_name": "={{ $json.body.city_name }}", "state": "={{ $json.body.state }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "city_name", "displayName": "city_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "state", "displayName": "state", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-620, 300], "id": "94e889c7-e546-451d-9661-47e1876d7799", "name": "Update single city", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.cities", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-940, 460], "id": "08ed02be-0a92-4c8c-b147-223fee0a1e8c", "name": "Get All citys", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "cities", "mode": "list", "cachedResultName": "cities"}, "returnAll": true, "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-920, 640], "id": "ca48a08f-5c45-48a4-95f3-46730ba1a059", "name": "Get single city", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## City Table API's\n\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 680, "width": 960, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-1060, 200], "typeVersion": 1, "id": "dcf1e385-e37b-4b3f-aae7-198fabf28740", "name": "Sticky Note1"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "cities", "mode": "list", "cachedResultName": "cities"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-620, 460], "id": "6a37cbb0-f2f8-41b1-a6bb-2cb59d9ff8b3", "name": "Postgres", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/babygame/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1920, 1000], "id": "f3fe4330-9753-448f-a45a-2cce1ed991d2", "name": "create-baby-game", "webhookId": "c45685f4-4341-4430-b697-c02a84e14a63"}, {"parameters": {"path": "v1/nibog/babygame/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1920, 1220], "id": "03abebc5-3120-4d3a-9236-c6326ab9a663", "name": "get all baby games", "webhookId": "c45685f4-4341-4430-b697-c02a84e14a63"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/babygame/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1400, 980], "id": "d1622969-1112-4d26-8518-79107cc14ca1", "name": "get single baby game", "webhookId": "c45685f4-4341-4430-b697-c02a84e14a63"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/babygame/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1440, 1240], "id": "5c28a759-2c42-40d9-b9c8-4d0c9ee99ef8", "name": "Update single baby game", "webhookId": "c45685f4-4341-4430-b697-c02a84e14a63"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/babygame/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1660, 1460], "id": "e2aa92ed-9155-474b-b869-4565ae7660ea", "name": "Delete single baby game", "webhookId": "c45685f4-4341-4430-b697-c02a84e14a63"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "baby_games", "mode": "list", "cachedResultName": "baby_games"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "game_name": "={{ $json.body.game_name }}", "min_age": "={{ $json.body.min_age_months }}", "max_age": "={{ $json.body.max_age_months }}", "description": "={{ $json.body.game_description }}", "duration_minutes": "={{ $json.body.duration_minutes }}", "categories": "={{ $json.body.categories }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "game_name", "displayName": "game_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "min_age", "displayName": "min_age", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "max_age", "displayName": "max_age", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "duration_minutes", "displayName": "duration_minutes", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "categories", "displayName": "categories", "required": false, "defaultMatch": false, "display": true, "type": "array", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1700, 1000], "id": "8484f555-4d62-412f-9a58-74a0cee582c7", "name": "add baby game", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM baby_games\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1760, 1220], "id": "b2cfad09-7edd-4027-ac2e-19a3f7441de0", "name": "Get all baby games", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "baby_games", "mode": "list", "cachedResultName": "baby_games"}, "returnAll": true, "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1180, 980], "id": "713f58f2-bc3f-4c08-8030-7a8e0dc23f2c", "name": "get single baby game1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "baby_games", "mode": "list", "cachedResultName": "baby_games"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "id": "={{ $json.body.id }}", "game_name": "={{ $json.body.game_name }}", "description": "={{ $json.body.description }}", "min_age": "={{ $json.body.min_age }}", "max_age": "={{ $json.body.max_age }}", "duration_minutes": "={{ $json.body.duration_minutes }}", "categories": "={{ $json.body.categories }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "game_name", "displayName": "game_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "min_age", "displayName": "min_age", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "max_age", "displayName": "max_age", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "duration_minutes", "displayName": "duration_minutes", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "categories", "displayName": "categories", "required": false, "defaultMatch": false, "display": true, "type": "array", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1220, 1240], "id": "6bcc6787-c432-49f1-9896-61fb71862c52", "name": "update single baby game", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "baby_games", "mode": "list", "cachedResultName": "baby_games"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1440, 1460], "id": "783e4964-474e-4875-8300-93554b90070a", "name": "Delete single baby game1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/venues/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-900, 1000], "id": "e265cca1-f527-49e1-8df1-9919ef19509f", "name": "Create venues", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "venues", "mode": "list", "cachedResultName": "venues"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "venue_name": "={{ $json.body.venue_name }}", "city_id": "={{ $json.body.city_id }}", "address": "={{ $json.body.address }}", "capacity": "={{ $json.body.capacity }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "venue_name", "displayName": "venue_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "capacity", "displayName": "capacity", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-800, 1000], "id": "07493e25-457d-43e0-8f59-75848b14cae0", "name": "create venues", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/venues/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-880, 1240], "id": "bad5e81b-24af-47cf-9934-80fd09ee070f", "name": "Get all venues", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM venues\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-780, 1240], "id": "bc7c92f3-33bf-4751-aeed-6b4a8e4b90ff", "name": "get all venues", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/venues/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-600, 960], "id": "7f693166-37f4-40a4-b2a1-5db6f5c0e01d", "name": "Get venue by ID", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "venues", "mode": "list", "cachedResultName": "venues"}, "returnAll": true, "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-500, 960], "id": "605a8ecd-4281-4178-9e22-e13b8a1bb409", "name": "Get venue by Id", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/venues/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-580, 1140], "id": "fd538d45-cf03-40e6-a1c9-46cbc96dd1cb", "name": "Update venue by ID", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "venues", "mode": "list", "cachedResultName": "venues"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "id": "={{ $json.body.id }}", "venue_name": "={{ $json.body.venue_name }}", "city_id": "={{ $json.body.city_id }}", "address": "={{ $json.body.address }}", "capacity": "={{ $json.body.capacity }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "venue_name", "displayName": "venue_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "capacity", "displayName": "capacity", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-480, 1140], "id": "47c77058-7741-4fcf-95ca-5f099450930f", "name": "Update venue by Id", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/venues/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-880, 1500], "id": "293d91d3-e4cd-4290-ad15-49f5b435eb6d", "name": "Delete venue by ID", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "venues", "mode": "list", "cachedResultName": "venues"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-780, 1500], "id": "0673589a-e293-4c95-ae81-c0ee3bab141c", "name": "Delete venue by Id", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## Venue API's \n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 760, "width": 1120, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-920, 900], "typeVersion": 1, "id": "7fac0c68-02de-40de-8fee-bd565d853f22", "name": "Sticky Note3"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/venues/get-by-city", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-540, 1340], "id": "ae8cb8e9-e1c2-45f4-9eb9-ed05b1752090", "name": "Get venues by City ID", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "venues", "mode": "list", "cachedResultName": "venues"}, "returnAll": true, "where": {"values": [{"column": "city_id", "value": "={{ $json.body.city_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-440, 1340], "id": "85d17992-d0ff-4634-8eac-45b0a6a4a75d", "name": "Get venues by city Id", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## Babay game API's\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 760, "width": 1100, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [-2040, 900], "typeVersion": 1, "id": "3d7c13e8-b917-446d-92ae-83b2342ef9b6", "name": "Sticky Note2"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1980, 1780], "id": "e1657a0b-a0b8-428c-a306-28a072e1cb45", "name": "Event create", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "events", "mode": "list", "cachedResultName": "events"}, "columns": {"mappingMode": "defineBelow", "value": {"title": "={{ $json.body.title }}", "description": "={{ $json.body.description }}", "city_id": 2, "venue_id": 2, "event_date": "={{ $json.body.event_date }}", "status": "={{ $json.body.status }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "title", "displayName": "title", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "venue_id", "displayName": "venue_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "event_date", "displayName": "event_date", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1800, 1780], "id": "a1b83d33-9e29-4fef-aa35-21c5cb31a06b", "name": "Event Create", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/event/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1980, 1980], "id": "c74a7e3c-b61c-42b9-9ac7-fa5f31b98b32", "name": "Get All Event", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  id,\n  title,\n  description,\n  city_id,\n  venue_id,\n\n  -- force a plain “YYYY-MM-DD” string:\n  event_date::text      AS event_date,\n\n  status,\n  created_at,\n  updated_at\nFROM events\nORDER BY id ASC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1800, 1980], "id": "23aff4ac-2da4-42b6-b26d-ee73317ad353", "name": "Get All events", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1500, 1780], "id": "b11363f3-17f7-4567-91d8-b6d065542619", "name": "Get Single Event", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  e.id AS event_id,\n  e.title,\n  e.description,\n  TO_CHAR(e.event_date, 'YYYY-MM-DD') AS event_date,\n  e.status,\n  TO_CHAR(e.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS') AS created_at_ist,\n  TO_CHAR(e.updated_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS') AS updated_at_ist,\n  c.city_name,\n  v.venue_name\nFROM events e\nJOIN cities c ON e.city_id = c.id\nJOIN venues v ON e.venue_id = v.id\nWHERE e.id = {{ $json[\"body\"][\"id\"] }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1320, 1780], "id": "9258bdd5-c63f-43ca-a7f7-9b2ea8737484", "name": "Get single event", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1980, 2200], "id": "9e85a875-bf69-4e61-b0d5-4962958cbb93", "name": "Event delete", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "events", "mode": "list", "cachedResultName": "events"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1780, 2200], "id": "bd0fb2b3-8c05-423e-a14d-3cd21ceb7614", "name": "Event Delete", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "events", "mode": "list", "cachedResultName": "events"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.body.id }}", "title": "={{ $json.body.title }}", "description": "={{ $json.body.description }}", "city_id": "={{ $json.body.city_id }}", "venue_id": "={{ $json.body.venue_id }}", "event_date": "={{ $json.body.event_date }}", "status": "={{ $json.body.status }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "title", "displayName": "title", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "venue_id", "displayName": "venue_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "event_date", "displayName": "event_date", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1320, 1980], "id": "4ac0e222-8cf4-47d6-923a-f919ddd94b3e", "name": "Event update", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1500, 1980], "id": "678db5c1-bae4-426d-afa8-e1a1900eb859", "name": "Event Update", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"content": "## Event API's\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 720, "width": 960, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-2040, 1680], "typeVersion": 1, "id": "d7ff40af-93a5-49c8-8d02-e37c7251f115", "name": "Sticky Note4"}, {"parameters": {"content": "## event_games_with_slots  API's\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 720, "width": 1100}, "type": "n8n-nodes-base.stickyNote", "position": [-1060, 1680], "typeVersion": 1, "id": "2f420cc5-8a5a-47c0-8abb-12e319d4fe22", "name": "Sticky Note5"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-game-slot/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1000, 1780], "id": "b05e386f-5bc1-446f-9adc-3bdc09af71fe", "name": "Event with game slot created", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"path": "v1/nibog/event-game-slot/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1000, 1980], "id": "fba73d11-42c0-413b-8af8-d076440ba1b1", "name": "Get All Event with game slot", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-game-slot/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-520, 1780], "id": "ff3eef82-6b2c-4a04-9514-4d42f71006ee", "name": "Get Single Event with game slot", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "event_games_with_slots", "mode": "list", "cachedResultName": "event_games_with_slots"}, "returnAll": true, "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-340, 1780], "id": "fe9c50f8-af4f-44f0-89fa-5cecca017e76", "name": "Get single Event with game slot", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-game-slot/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-520, 1980], "id": "d956d757-c79f-4b38-97d5-71a6bd0ac7c3", "name": "Event with game slot Update", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-game-slot/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-740, 2200], "id": "ca3af062-81ce-4f91-90ae-f6f7f0001124", "name": "Event with game slot delete", "webhookId": "2dc9dc4c-b529-4bc7-a190-b91bcede3d33"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "event_games_with_slots", "mode": "list", "cachedResultName": "event_games_with_slots"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-560, 2200], "id": "c8ed0cbb-4ef1-4332-8f78-e172b84f6123", "name": "Event with game slot Delete", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM event_games_with_slots\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-800, 1980], "id": "96254ad0-0b45-4469-af3f-f938b148890c", "name": "Get All event with game slot", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "event_games_with_slots", "mode": "list", "cachedResultName": "event_games_with_slots"}, "columns": {"mappingMode": "defineBelow", "value": {"event_id": "={{ $json.body.event_id }}", "game_id": "={{ $json.body.game_id }}", "custom_title": "={{ $json.body.custom_title }}", "custom_description": "={{ $json.body.custom_description }}", "custom_price": "={{ $json.body.custom_price }}", "start_time": "={{ $json.body.start_time }}", "end_time": "={{ $json.body.end_time }}", "slot_price": "={{ $json.body.slot_price }}", "max_participants": "={{ $json.body.max_participants }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "event_id", "displayName": "event_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "game_id", "displayName": "game_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "custom_title", "displayName": "custom_title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "custom_description", "displayName": "custom_description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "custom_price", "displayName": "custom_price", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "start_time", "displayName": "start_time", "required": true, "defaultMatch": false, "display": true, "type": "time", "canBeUsedToMatch": true}, {"id": "end_time", "displayName": "end_time", "required": true, "defaultMatch": false, "display": true, "type": "time", "canBeUsedToMatch": true}, {"id": "slot_price", "displayName": "slot_price", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "max_participants", "displayName": "max_participants", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-840, 1780], "id": "f22ae780-10ec-42ef-a03c-91d83c8ef4d5", "name": "Event with game slot Create", "alwaysOutputData": true, "executeOnce": false, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "event_games_with_slots", "mode": "list", "cachedResultName": "event_games_with_slots"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.body.id }}", "event_id": "={{ $json.body.event_id }}", "game_id": "={{ $json.body.game_id }}", "custom_title": "={{ $json.body.custom_title }}", "custom_description": "={{ $json.body.custom_description }}", "custom_price": "={{ $json.body.custom_price }}", "end_time": "={{ $json.body.end_time }}", "start_time": "={{ $json.body.start_time }}", "max_participants": "={{ $json.body.max_participants }}", "slot_price": "={{ $json.body.slot_price }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "event_id", "displayName": "event_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "game_id", "displayName": "game_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "custom_title", "displayName": "custom_title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "custom_description", "displayName": "custom_description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "custom_price", "displayName": "custom_price", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "start_time", "displayName": "start_time", "required": true, "defaultMatch": false, "display": true, "type": "time", "canBeUsedToMatch": true}, {"id": "end_time", "displayName": "end_time", "required": true, "defaultMatch": false, "display": true, "type": "time", "canBeUsedToMatch": true}, {"id": "slot_price", "displayName": "slot_price", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "max_participants", "displayName": "max_participants", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-360, 1980], "id": "4332d56d-ab9c-4c8e-8519-f3ec43214039", "name": "Event with game slot update", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/employee/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1040, 2540], "id": "4a7f7178-90e4-48ae-a683-8aa143191874", "name": "Employee create", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "employee", "mode": "list", "cachedResultName": "employee"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "is_superadmin": "={{ $json.body.is_superadmin }}", "employee_id": "={{ $json.body.employee_id }}", "department": "={{ $json.body.department }}", "designation": "={{ $json.body.designation }}", "qualification": "={{ $json.body.qualification }}", "work_exp": "={{ $json.body.work_exp }}", "name": "={{ $json.body.name }}", "surname": "={{ $json.body.surname }}", "father_name": "={{ $json.body.father_name }}", "mother_name": "={{ $json.body.mother_name }}", "contact_no": "={{ $json.body.contact_no }}", "emeregency_contact_no": "={{ $json.body.emeregency_contact_no }}", "email": "={{ $json.body.email }}", "dob": "={{ $json.body.dob }}", "marital_status": "={{ $json.body.marital_status }}", "date_of_joining": "={{ $json.body.date_of_joining }}", "local_address": "={{ $json.body.local_address }}", "permanent_address": "={{ $json.body.permanent_address }}", "password": "={{ $json.body.password }}", "gender": "={{ $json.body.gender }}", "acount_title": "={{ $json.body.acount_title }}", "bank_account_no": "={{ $json.body.bank_account_no }}", "bank_name": "={{ $json.body.bank_name }}", "ifsc_code": "={{ $json.body.ifsc_code }}", "payscale": "={{ $json.body.payscale }}", "bank_branch": "={{ $json.body.bank_branch }}", "basic_salary": "={{ $json.body.basic_salary }}", "epf_no": "={{ $json.body.epf_no }}", "contract_type": "={{ $json.body.contract_type }}", "shift": "={{ $json.body.shift }}", "location": "={{ $json.body.location }}", "resume": "={{ $json.body.resume }}", "other_document_file": "={{ $json.body.other_document_file }}", "other_document_name": "={{ $json.body.other_document_name }}", "resignation_letter": "={{ $json.body.resignation_letter }}", "joining_letter": "={{ $json.body.joining_letter }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "employee_id", "displayName": "employee_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "department", "displayName": "department", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "designation", "displayName": "designation", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "qualification", "displayName": "qualification", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "work_exp", "displayName": "work_exp", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "surname", "displayName": "surname", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "father_name", "displayName": "father_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "mother_name", "displayName": "mother_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contact_no", "displayName": "contact_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "emeregency_contact_no", "displayName": "emeregency_contact_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "dob", "displayName": "dob", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "marital_status", "displayName": "marital_status", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "date_of_joining", "displayName": "date_of_joining", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "date_of_leaving", "displayName": "date_of_leaving", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "local_address", "displayName": "local_address", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "permanent_address", "displayName": "permanent_address", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "note", "displayName": "note", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "image", "displayName": "image", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "password", "displayName": "password", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "gender", "displayName": "gender", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "acount_title", "displayName": "acount_title", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "bank_account_no", "displayName": "bank_account_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "bank_name", "displayName": "bank_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "ifsc_code", "displayName": "ifsc_code", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "bank_branch", "displayName": "bank_branch", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "payscale", "displayName": "payscale", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "basic_salary", "displayName": "basic_salary", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "epf_no", "displayName": "epf_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contract_type", "displayName": "contract_type", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "shift", "displayName": "shift", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "location", "displayName": "location", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "resume", "displayName": "resume", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "joining_letter", "displayName": "joining_letter", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "resignation_letter", "displayName": "resignation_letter", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "other_document_name", "displayName": "other_document_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "other_document_file", "displayName": "other_document_file", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "is_superadmin", "displayName": "is_superadmin", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "verification_code", "displayName": "verification_code", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-840, 2540], "id": "5d498aac-ffc0-4901-9651-414e17017ca0", "name": "employee create", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/employee/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1020, 2740], "id": "7a1f9322-70ad-481b-abe3-c7bae0bd6573", "name": "get single employee", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "employee", "mode": "list", "cachedResultName": "employee"}, "returnAll": true, "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-820, 2740], "id": "8984e4ac-83cd-45f2-b6d3-2799ed4e1903", "name": "Get single employee", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/employee/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-560, 2540], "id": "da82bed9-e42f-45d3-a760-6b0780a97a29", "name": "Get all employee", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.employee\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-380, 2540], "id": "83829c20-175c-4528-ae97-2dd43a036165", "name": "get all employee", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/employee/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-560, 2760], "id": "c300621f-9c07-4b0d-968b-891c99992da1", "name": "Update employee", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "employee", "mode": "list", "cachedResultName": "employee"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "is_superadmin": "={{ $json.body.is_superadmin }}", "id": "={{ $json.body.id }}", "employee_id": "={{ $json.body.employee_id }}", "department": "={{ $json.body.department }}", "designation": "={{ $json.body.designation }}", "qualification": "={{ $json.body.qualification }}", "work_exp": "={{ $json.body.work_exp }}", "name": "={{ $json.body.name }}", "surname": "={{ $json.body.surname }}", "father_name": "={{ $json.body.father_name }}", "mother_name": "={{ $json.body.mother_name }}", "contact_no": "={{ $json.body.contact_no }}", "emeregency_contact_no": "={{ $json.body.emeregency_contact_no }}", "email": "={{ $json.body.email }}", "dob": "={{ $json.body.dob }}", "marital_status": "={{ $json.body.marital_status }}", "date_of_joining": "={{ $json.body.date_of_joining }}", "local_address": "={{ $json.body.local_address }}", "permanent_address": "={{ $json.body.permanent_address }}", "note": "={{ $json.body.note }}", "image": "=", "password": "={{ $json.body.password }}", "gender": "={{ $json.body.gender }}", "acount_title": "={{ $json.body.acount_title }}", "bank_account_no": "={{ $json.body.bank_account_no }}", "bank_name": "={{ $json.body.bank_name }}", "ifsc_code": "={{ $json.body.ifsc_code }}", "bank_branch": "={{ $json.body.bank_branch }}", "payscale": "={{ $json.body.payscale }}", "basic_salary": "={{ $json.body.basic_salary }}", "epf_no": "={{ $json.body.epf_no }}", "contract_type": "={{ $json.body.contract_type }}", "shift": "={{ $json.body.shift }}", "location": "={{ $json.body.location }}", "resume": "={{ $json.body.resume }}", "resignation_letter": "={{ $json.body.resignation_letter }}", "joining_letter": "={{ $json.body.joining_letter }}", "other_document_file": "={{ $json.body.other_document_file }}", "other_document_name": "={{ $json.body.other_document_name }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "employee_id", "displayName": "employee_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "department", "displayName": "department", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "designation", "displayName": "designation", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "qualification", "displayName": "qualification", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "work_exp", "displayName": "work_exp", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "surname", "displayName": "surname", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "father_name", "displayName": "father_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "mother_name", "displayName": "mother_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contact_no", "displayName": "contact_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "emeregency_contact_no", "displayName": "emeregency_contact_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "dob", "displayName": "dob", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "marital_status", "displayName": "marital_status", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "date_of_joining", "displayName": "date_of_joining", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "date_of_leaving", "displayName": "date_of_leaving", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "local_address", "displayName": "local_address", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "permanent_address", "displayName": "permanent_address", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "note", "displayName": "note", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "image", "displayName": "image", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "password", "displayName": "password", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "gender", "displayName": "gender", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "acount_title", "displayName": "acount_title", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "bank_account_no", "displayName": "bank_account_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "bank_name", "displayName": "bank_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "ifsc_code", "displayName": "ifsc_code", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "bank_branch", "displayName": "bank_branch", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "payscale", "displayName": "payscale", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "basic_salary", "displayName": "basic_salary", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "epf_no", "displayName": "epf_no", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contract_type", "displayName": "contract_type", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "shift", "displayName": "shift", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "location", "displayName": "location", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "resume", "displayName": "resume", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "joining_letter", "displayName": "joining_letter", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "resignation_letter", "displayName": "resignation_letter", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "other_document_name", "displayName": "other_document_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "other_document_file", "displayName": "other_document_file", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "is_superadmin", "displayName": "is_superadmin", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "verification_code", "displayName": "verification_code", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-400, 2760], "id": "f5487558-96f3-4cc4-86a6-f96ed0a390f3", "name": "update employee", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## Employee API's \n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 900, "width": 1120, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-1100, 2440], "typeVersion": 1, "id": "********-ee5e-418d-b478-10e7a0faa3ea", "name": "Sticky Note6"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/employee/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-960, 2940], "id": "1b07d7ab-9fa9-437e-b6e2-750d191459c7", "name": "Delete single employee", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "employee", "mode": "list", "cachedResultName": "employee"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-760, 2940], "id": "61bc781f-96cd-4f57-a8fc-ff0e04491768", "name": "delete single employee", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/venues/getall-with-city", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-280, 960], "id": "7b8a390e-2dad-481e-899b-d867492676ee", "name": "Get all venues with city", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n    venues.id AS venue_id,\n    venues.venue_name,\n    venues.address,\n    venues.capacity,\n    venues.is_active AS venue_is_active,\n    venues.created_at AS venue_created_at,\n    venues.updated_at AS venue_updated_at,\n    cities.id AS city_id,\n    cities.city_name,\n    cities.state,\n    cities.is_active AS city_is_active,\n    cities.created_at AS city_created_at,\n    cities.updated_at AS city_updated_at\nFROM \n    venues\nJOIN \n    cities ON venues.city_id = cities.id\nWHERE \n    venues.is_active = TRUE AND cities.is_active = TRUE;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-180, 960], "id": "0dc2b9d0-d081-4a58-935f-00b602b3e319", "name": "get all venues with city", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO event_games_with_slots (\n  event_id,\n  game_id,\n  custom_title,\n  custom_description,\n  custom_price,\n  start_time,\n  end_time,\n  slot_price,\n  max_participants\n) VALUES (\n  {{ $('Event registration').item.json.id }},\n  {{ $json.game_id }},\n  '{{ $json.custom_title }}',\n  '{{ $json.custom_description }}',\n  {{ $json.custom_price }},\n  '{{ $json.start_time }}',\n  '{{ $json.end_time }}',\n  {{ $json.slot_price }},\n  {{ $json.max_participants }}\n);\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1400, 2520], "id": "07be54f4-aff2-408b-b885-ffb2652e127f", "name": "Postgres2", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nreturn $('event registration').first().json.body.games;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1600, 2520], "id": "46adddab-9504-44fd-9d5f-5ec5fb058b62", "name": "Code", "alwaysOutputData": true}, {"parameters": {"httpMethod": "POST", "path": "/v1/nibog/socialmedia/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [20, 520], "id": "50465a24-8514-437e-ad18-c8f093268af8", "name": "Social media create", "webhookId": "4e39fde8-c11c-49fc-82b0-dfc4a174e0d9"}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "social_media_settings", "mode": "list", "cachedResultName": "social_media_settings"}, "columns": {"mappingMode": "defineBelow", "value": {"facebook_url": "={{ $json.body.facebook_url }}", "instagram_url": "={{ $json.body.instagram_url }}", "twitter_url": "={{ $json.body.twitter_url }}", "youtube_url": "={{ $json.body.youtube_url }}", "id": 1}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "facebook_url", "displayName": "facebook_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "instagram_url", "displayName": "instagram_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "twitter_url", "displayName": "twitter_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "youtube_url", "displayName": "youtube_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [200, 520], "id": "317898f6-8d95-4d9c-b1c0-cf0673c3e040", "name": "social media", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.social_media_settings\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [200, 700], "id": "d082f11e-4c47-4e5e-8b95-9f614286ea76", "name": "social meda get", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "/v1/nibog/socialmedia/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [20, 700], "id": "72c2799f-a22e-4aef-8aec-37fb9eba35b7", "name": "Social media get", "webhookId": "4e39fde8-c11c-49fc-82b0-dfc4a174e0d9"}, {"parameters": {"content": "## Social media\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 440, "width": 520, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [-80, 440], "typeVersion": 1, "id": "fc72756b-c441-4552-8458-************", "name": "Sticky Note7"}, {"parameters": {"content": "## Email Settings\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 480, "width": 520}, "type": "n8n-nodes-base.stickyNote", "position": [-80, -60], "typeVersion": 1, "id": "21add8ca-6bcd-4f67-8290-05cbbbbb3d72", "name": "Sticky Note8"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/emailsetting/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [20, 40], "id": "d2d79e16-4197-460b-8710-de7475c3b439", "name": "Email setting create", "webhookId": "4e39fde8-c11c-49fc-82b0-dfc4a174e0d9"}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "email_settings", "mode": "list", "cachedResultName": "email_settings"}, "columns": {"mappingMode": "defineBelow", "value": {"id": 1, "smtp_host": "={{ $json.body.smtp_host }}", "smtp_port": "={{ $json.body.smtp_port }}", "smtp_username": "={{ $json.body.smtp_username }}", "smtp_password": "={{ $json.body.smtp_password }}", "sender_name": "={{ $json.body.sender_name }}", "sender_email": "={{ $json.body.sender_email }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "smtp_host", "displayName": "smtp_host", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "smtp_port", "displayName": "smtp_port", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": false}, {"id": "smtp_username", "displayName": "smtp_username", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "smtp_password", "displayName": "smtp_password", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "sender_name", "displayName": "sender_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "sender_email", "displayName": "sender_email", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [200, 40], "id": "ab9061e6-3354-4bee-8ce8-0a4e2605f92b", "name": "email setting create", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.email_settings\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [200, 220], "id": "22f86bc7-7b45-454a-a6d2-37d0c54d9654", "name": "email setting get", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/emailsetting/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [20, 220], "id": "a4d00eaa-4641-498f-be2f-5801a72dcdc5", "name": "Email setting get", "webhookId": "4e39fde8-c11c-49fc-82b0-dfc4a174e0d9"}, {"parameters": {"content": "## General Settings\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 460, "width": 520, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [460, -60], "typeVersion": 1, "id": "5cebc690-173e-464e-9cb3-d73d1ba28d07", "name": "Sticky Note9"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/generalsetting/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [560, 40], "id": "827fa6c7-780a-44bd-85d4-0d1f8daee91d", "name": "General setting create", "webhookId": "4e39fde8-c11c-49fc-82b0-dfc4a174e0d9"}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "general_settings", "mode": "list", "cachedResultName": "general_settings"}, "columns": {"mappingMode": "defineBelow", "value": {"id": 1, "site_name": "={{ $json.body.site_name }}", "site_tagline": "={{ $json.body.site_tagline }}", "contact_email": "={{ $json.body.contact_email }}", "contact_phone": "={{ $json.body.contact_phone }}", "address": "={{ $json.body.address }}", "logo_path": "={{ $json.body.logo }}", "favicon_path": "={{ $json.body.favicon }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "site_name", "displayName": "site_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "site_tagline", "displayName": "site_tagline", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "contact_email", "displayName": "contact_email", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "contact_phone", "displayName": "contact_phone", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "address", "displayName": "address", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "logo_path", "displayName": "logo_path", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "favicon_path", "displayName": "favicon_path", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [740, 40], "id": "2a87a67f-52ab-45c9-8d55-d50f4537f360", "name": "general setting create", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/generalsetting/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [560, 220], "id": "1b37041d-9bf7-47a4-ab6d-6a947ed9c8fe", "name": "General setting get", "webhookId": "4e39fde8-c11c-49fc-82b0-dfc4a174e0d9"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.general_settings\nORDER BY id ASC", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [740, 220], "id": "9db0af4f-9efc-4412-9015-a54443065e21", "name": "general setting get", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-registration/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2000, 2520], "id": "134572be-a3f6-4abc-b6cd-0111f2c0f39b", "name": "event registration", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"path": "v1/nibog/event-registration/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2040, 2700], "id": "e6b3d4be-c800-416d-b061-f4594f711fad", "name": "event rgistration get all", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "events", "mode": "list", "cachedResultName": "events"}, "columns": {"mappingMode": "defineBelow", "value": {"title": "={{ $json.body.title }}", "description": "={{ $json.body.description }}", "city_id": "={{ $json.body.city_id }}", "venue_id": "={{ $json.body.venue_id }}", "event_date": "={{ $json.body.event_date }}", "status": "={{ $json.body.status }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "title", "displayName": "title", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "venue_id", "displayName": "venue_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "event_date", "displayName": "event_date", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1760, 2520], "id": "b4677f5e-b02b-47e2-92ab-ebc815ad1997", "name": "Event registration", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  e.id                       AS event_id,\n  e.title                    AS event_title,\n  e.description              AS event_description,\n\n  -- emit exactly \"2025-08-15\" rather than a JS Date at midnight UTC\n  e.event_date::text         AS event_date,  \n\n  e.status                   AS event_status,\n  e.created_at               AS event_created_at,\n  e.updated_at               AS event_updated_at,\n\n  c.id                       AS city_id,\n  c.city_name,\n  c.state,\n  c.is_active                AS city_is_active,\n  c.created_at               AS city_created_at,\n  c.updated_at               AS city_updated_at,\n\n  v.id                       AS venue_id,\n  v.venue_name,\n  v.address                  AS venue_address,\n  v.capacity                 AS venue_capacity,\n  v.is_active                AS venue_is_active,\n  v.created_at               AS venue_created_at,\n  v.updated_at               AS venue_updated_at,\n\n  COALESCE(\n    json_agg(\n      json_build_object(\n        'game_id',           bg.id,\n        'game_title',        bg.game_name,\n        'game_description',  bg.description,\n        'min_age',           bg.min_age,\n        'max_age',           bg.max_age,\n        'game_duration_minutes', bg.duration_minutes,\n        'categories',        bg.categories,\n        'custom_title',      eg.custom_title,\n        'custom_description',eg.custom_description,\n        'custom_price',      eg.custom_price,\n        'start_time',        eg.start_time,\n        'end_time',          eg.end_time,\n        'slot_price',        eg.slot_price,\n        'max_participants',  eg.max_participants\n      )\n      ORDER BY eg.start_time\n    ),\n    '[]'::json\n  ) AS games\n\nFROM\n  events e\n  JOIN cities c                   ON e.city_id  = c.id\n  JOIN venues v                   ON e.venue_id = v.id\n  LEFT JOIN event_games_with_slots eg ON e.id    = eg.event_id\n  LEFT JOIN baby_games bg            ON eg.game_id = bg.id\n\nGROUP BY\n  e.id, c.id, v.id\n\n-- still order by the true date column\nORDER BY\n  e.event_date;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1840, 2700], "id": "********-4b32-40b3-bb23-5b80f2733b5c", "name": "event registration get all", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-registration/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1600, 2700], "id": "8dbe6603-7c3d-4086-a791-01df9bd12627", "name": "event registration single get", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  e.id AS event_id,\n  e.title AS event_title,\n  e.description AS event_description,\n  TO_CHAR(e.event_date, 'YYYY-MM-DD') AS event_date,\n  e.status AS event_status,\n  e.created_at AS event_created_at,\n  e.updated_at AS event_updated_at,\n\n  c.id AS city_id,\n  c.city_name,\n  c.state,\n  c.is_active AS city_is_active,\n  c.created_at AS city_created_at,\n  c.updated_at AS city_updated_at,\n\n  v.id AS venue_id,\n  v.venue_name,\n  v.address AS venue_address,\n  v.capacity AS venue_capacity,\n  v.is_active AS venue_is_active,\n  v.created_at AS venue_created_at,\n  v.updated_at AS venue_updated_at,\n\n  COALESCE(\n    json_agg(\n      json_build_object(\n        'game_id', bg.id,\n        'game_title', bg.game_name,\n        'game_description', bg.description,\n        'min_age', bg.min_age,\n        'max_age', bg.max_age,\n        'game_duration_minutes', bg.duration_minutes,\n        'categories', bg.categories,\n        'custom_title', eg.custom_title,\n        'custom_description', eg.custom_description,\n        'custom_price', eg.custom_price,\n        'start_time', eg.start_time,\n        'end_time', eg.end_time,\n        'slot_price', eg.slot_price,\n        'max_participants', eg.max_participants\n      )\n      ORDER BY eg.start_time\n    ) \n    , '[]'::json\n  ) AS games\n\nFROM\n  events e\nJOIN\n  cities c ON e.city_id = c.id\nJOIN\n  venues v ON e.venue_id = v.id\nLEFT JOIN\n  event_games_with_slots eg ON e.id = eg.event_id\nLEFT JOIN\n  baby_games bg ON eg.game_id = bg.id\n\nWHERE\n  e.id =  {{ $json.body.id }}-- Filter by specific event_id\n\nGROUP BY\n  e.id, c.id, v.id\n\nORDER BY\n  e.event_date;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1400, 2700], "id": "0ad79ecc-2215-4cf4-ac32-538c34c51f56", "name": "Event registration single get", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-registration/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2100, 2880], "id": "126e9a85-2d63-4c2e-a108-e5e1fb11f06a", "name": "event registration del", "webhookId": "71ffbbc9-228f-4e68-a36d-97f7fa028972"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN;\n  DELETE FROM event_games_with_slots WHERE event_id ={{ $json.body.id }} ;\n  DELETE FROM events WHERE id = {{ $json.body.id }};\nCOMMIT;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1940, 2880], "id": "54931e54-5f55-4d63-a719-472557baa911", "name": "event register del", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## event registration \n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 840, "width": 1040}, "type": "n8n-nodes-base.stickyNote", "position": [-2160, 2440], "typeVersion": 1, "id": "1f040229-034e-4707-be10-14d8153239ce", "name": "Sticky Note10"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nreturn $('event registration update').first().json.body.games;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1520, 3060], "id": "2f9c37e1-62ec-4ed7-89dd-171eba1236ba", "name": "Code1", "alwaysOutputData": true}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-registration/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2100, 3060], "id": "f7e523c1-e5c1-4093-9073-f050a99ff410", "name": "event registration update", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "events", "mode": "list", "cachedResultName": "events"}, "columns": {"mappingMode": "defineBelow", "value": {"title": "={{ $json.body.title }}", "description": "={{ $json.body.description }}", "city_id": "={{ $json.body.city_id }}", "venue_id": "={{ $json.body.venue_id }}", "event_date": "={{ $json.body.event_date }}", "status": "={{ $json.body.status }}", "id": "={{ $json.body.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "venue_id", "displayName": "venue_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "event_date", "displayName": "event_date", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1900, 3060], "id": "05d3636e-7604-4315-b2e6-286409b980f6", "name": "Event registration update", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM event_games_with_slots WHERE event_id ={{ $json.id }} ;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1700, 3060], "id": "f12a50b1-ee38-4adc-8570-c5d6c28c4b38", "name": "del exisiting data", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO event_games_with_slots (\n  event_id,\n  game_id,\n  custom_title,\n  custom_description,\n  custom_price,\n  start_time,\n  end_time,\n  slot_price,\n  max_participants\n) VALUES (\n  {{ $('Event registration update').item.json.id }},\n  {{ $json.game_id }},\n  '{{ $json.custom_title }}',\n  '{{ $json.custom_description }}',\n  {{ $json.custom_price }},\n  '{{ $json.start_time }}',\n  '{{ $json.end_time }}',\n  {{ $json.slot_price }},\n  {{ $json.max_participants }}\n);\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1360, 3060], "id": "1615c237-c5d6-4177-9213-a87fa5b2512e", "name": "Event registration update2", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-registration/getbycityid", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1680, 2900], "id": "f772676a-980e-4f0f-9ef4-a74557dced61", "name": "get event registration by city id", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  e.id AS event_id,\n  e.title AS event_title,\n  e.description AS event_description,\n  e.event_date,\n  e.status AS event_status,\n  e.created_at AS event_created_at,\n  e.updated_at AS event_updated_at,\n\n  c.id AS city_id,\n  c.city_name,\n  c.state,\n  c.is_active AS city_is_active,\n  c.created_at AS city_created_at,\n  c.updated_at AS city_updated_at,\n\n  v.id AS venue_id,\n  v.venue_name,\n  v.address AS venue_address,\n  v.capacity AS venue_capacity,\n  v.is_active AS venue_is_active,\n  v.created_at AS venue_created_at,\n  v.updated_at AS venue_updated_at,\n\n  COALESCE(\n    json_agg(\n      json_build_object(\n        'game_id', bg.id,\n        'game_title', bg.game_name,\n        'game_description', bg.description,\n        'min_age', bg.min_age,\n        'max_age', bg.max_age,\n        'game_duration_minutes', bg.duration_minutes,\n        'categories', bg.categories,\n        'custom_title', eg.custom_title,\n        'custom_description', eg.custom_description,\n        'custom_price', eg.custom_price,\n        'start_time', eg.start_time,\n        'end_time', eg.end_time,\n        'slot_price', eg.slot_price,\n        'max_participants', eg.max_participants\n      )\n      ORDER BY eg.start_time\n    ) \n    , '[]'::json\n  ) AS games\n\nFROM\n  events e\nJOIN\n  cities c ON e.city_id = c.id\nJOIN\n  venues v ON e.venue_id = v.id\nLEFT JOIN\n  event_games_with_slots eg ON e.id = eg.event_id\nLEFT JOIN\n  baby_games bg ON eg.game_id = bg.id\n\nWHERE\n  c.id = {{ $json.body.city_id }}  -- Replace 5 with your desired city_id\n\nGROUP BY\n  e.id, c.id, v.id\n\nORDER BY\n  e.event_date;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1460, 2900], "id": "e6ecadce-fc8c-4228-9472-940fdae91b04", "name": "event registration get all1", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/user/login", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [560, 540], "id": "64cf6340-cf6c-46c2-83e9-6f728a9461c9", "name": "user login", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/user/register", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [560, 720], "id": "fa8f119d-f0fa-4e24-81ad-eac4d1b238e4", "name": "user register", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "columns": {"mappingMode": "defineBelow", "value": {"email_verified": false, "phone_verified": false, "accepted_terms": false, "is_active": true, "is_locked": false, "full_name": "={{ $json.body.full_name }}", "email": "={{ $json.body.email }}", "phone": "={{ $json.body.phone }}", "password_hash": "={{ $json.body.password }}", "city_id": "={{ $json.body.city_id }}"}, "matchingColumns": [], "schema": [{"id": "user_id", "displayName": "user_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "full_name", "displayName": "full_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email_verified", "displayName": "email_verified", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_verified", "displayName": "phone_verified", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "password_hash", "displayName": "password_hash", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "accepted_terms", "displayName": "accepted_terms", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "terms_accepted_at", "displayName": "terms_accepted_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "is_locked", "displayName": "is_locked", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "locked_until", "displayName": "locked_until", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "deactivated_at", "displayName": "deactivated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "last_login_at", "displayName": "last_login_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [780, 720], "id": "dd118579-8f09-4540-ae22-6acbb16b4301", "name": "registration", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "returnAll": true, "where": {"values": [{"column": "email", "value": "={{ $json.body.email }}"}, {"column": "password_hash", "value": "={{ $json.body.password }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [780, 540], "id": "369d2301-01a1-49ee-ab0d-f2cfbbf6e520", "name": "User login", "alwaysOutputData": true, "notesInFlow": false, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const user = $input.first().json;\nreturn [\n  {\n    json: {\n      object: user\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [960, 540], "id": "9fff5e2f-8d6d-4350-a7d9-5c9278a66e8d", "name": "Code2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ff843cfa-8ec9-4875-8231-89ef5fdc2447", "leftValue": "={{ $json.object }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1160, 540], "id": "086b042b-a637-41b6-834f-703f1ebde116", "name": "If"}, {"parameters": {"content": "## User authentications\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 660, "width": 1020}, "type": "n8n-nodes-base.stickyNote", "position": [460, 420], "typeVersion": 1, "id": "f9809aff-f461-4fc8-872a-286c621f6554", "name": "Sticky Note11"}, {"parameters": {"path": "v1/nibog/user/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1060, 700], "id": "6af66653-01a7-43b2-af0c-c05f460d7b9b", "name": "user list", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  u.*,\n  c.city_name,\n  c.state\nFROM \n  public.users u\nLEFT JOIN \n  public.cities c \nON \n  u.city_id = c.id\nORDER BY \n  u.user_id ASC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1220, 700], "id": "8c6bcdc3-5a97-4183-9a60-2a6b12803d2d", "name": "get users list", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/user/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [580, 900], "id": "bf5ecc63-015d-4584-ae46-8ae387e7f6f3", "name": "user del", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "deleteCommand": "delete", "where": {"values": [{"column": "user_id", "value": "={{ $json.body.user_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [800, 900], "id": "d28cb142-3d6a-494d-a6c5-49b73d8c3cb2", "name": "del user", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/user/edit", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1060, 900], "id": "5a159181-6018-4837-92cb-7df7e4855425", "name": "user edit", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "users", "mode": "list", "cachedResultName": "users"}, "columns": {"mappingMode": "defineBelow", "value": {"email_verified": false, "phone_verified": false, "accepted_terms": "={{ $json.body.accept_terms }}", "is_active": true, "is_locked": false, "user_id": "={{ $json.body.user_id }}", "full_name": "={{ $json.body.full_name }}", "email": "={{ $json.body.email }}", "phone": "={{ $json.body.phone }}", "password_hash": "={{ $json.body.password }}", "city_id": "={{ $json.body.city_id }}"}, "matchingColumns": ["user_id"], "schema": [{"id": "user_id", "displayName": "user_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "full_name", "displayName": "full_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email_verified", "displayName": "email_verified", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_verified", "displayName": "phone_verified", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "password_hash", "displayName": "password_hash", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city_id", "displayName": "city_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "accepted_terms", "displayName": "accepted_terms", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "terms_accepted_at", "displayName": "terms_accepted_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "is_locked", "displayName": "is_locked", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "locked_until", "displayName": "locked_until", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "deactivated_at", "displayName": "deactivated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "last_login_at", "displayName": "last_login_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1240, 900], "id": "ee476a3d-a3eb-424b-891b-cf463626c54e", "name": "edit user", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/bookingsevents/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [360, 1240], "id": "910c538b-bd39-4ecc-81aa-06e5391faf21", "name": "bookings register", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "booking_games", "mode": "list", "cachedResultName": "booking_games"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "booking_id": "={{ $json.booking_id }}", "child_id": "={{ $json.child_id }}", "game_id": "={{ $json.game_id }}", "game_price": "={{ $json.game_price }}", "attendance_status": "Registered", "slot_id": "={{ $json.slot_id }}"}, "matchingColumns": [], "schema": [{"id": "booking_game_id", "displayName": "booking_game_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "booking_id", "displayName": "booking_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "child_id", "displayName": "child_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "game_id", "displayName": "game_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "game_price", "displayName": "game_price", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "attendance_status", "displayName": "attendance_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "slot_id", "displayName": "slot_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1220, 1240], "id": "47fa716a-5439-4046-a6de-4b95732d151b", "name": "booking games inserted", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/bookingsevents/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [400, 1420], "id": "1e0bf30a-3125-4816-a4ee-cd3888d6e386", "name": "get all bookings", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  -- Booking\n  b.booking_id,\n  b.booking_ref,\n  b.status AS booking_status,\n  b.total_amount::TEXT,\n  b.payment_method,\n  b.payment_status,\n  b.terms_accepted,\n  b.is_active AS booking_is_active,\n  b.created_at AS booking_created_at,\n  b.updated_at AS booking_updated_at,\n  b.cancelled_at,\n  b.completed_at,\n\n  -- Parent\n  p.parent_id,\n  p.parent_name,\n  p.email AS parent_email,\n  p.additional_phone AS parent_additional_phone,\n  p.is_active AS parent_is_active,\n  p.created_at AS parent_created_at,\n  p.updated_at AS parent_updated_at,\n\n  -- Child\n  c.child_id,\n  c.full_name AS child_full_name,\n  c.date_of_birth AS child_date_of_birth,\n  c.school_name AS child_school_name,\n  c.gender AS child_gender,\n  c.is_active AS child_is_active,\n  c.created_at AS child_created_at,\n  c.updated_at AS child_updated_at,\n\n  -- Game\n  g.game_name,\n  g.description AS game_description,\n  g.min_age AS game_min_age,\n  g.max_age AS game_max_age,\n  g.duration_minutes AS game_duration_minutes,\n  g.categories,\n  g.is_active AS game_is_active,\n  g.created_at AS game_created_at,\n  g.updated_at AS game_updated_at,\n\n  -- Booking-Game join details\n  bg.game_price,\n  bg.attendance_status,\n\n  -- Event\n  e.title AS event_title,\n  e.description AS event_description,\n  e.event_date AS event_event_date,\n  e.status AS event_status,\n  e.created_at AS event_created_at,\n  e.updated_at AS event_updated_at,\n\n  -- User (booking.user_id)\n  u.full_name AS user_full_name,\n  u.email AS user_email,\n  u.phone AS user_phone,\n  u.city_id AS user_city_id,\n  u.accepted_terms AS user_accepted_terms,\n  u.terms_accepted_at AS user_terms_accepted_at,\n  u.is_active AS user_is_active,\n  u.is_locked AS user_is_locked,\n  u.locked_until AS user_locked_until,\n  u.deactivated_at AS user_deactivated_at,\n  u.created_at AS user_created_at,\n  u.updated_at AS user_updated_at,\n  u.last_login_at AS user_last_login_at,\n\n  -- City\n  ci.city_name,\n  ci.state AS city_state,\n  ci.is_active AS city_is_active,\n  ci.created_at AS city_created_at,\n  ci.updated_at AS city_updated_at,\n\n  -- Venue\n  v.venue_name,\n  v.address AS venue_address,\n  v.capacity AS venue_capacity,\n  v.is_active AS venue_is_active,\n  v.created_at AS venue_created_at,\n  v.updated_at AS venue_updated_at\n\nFROM booking_games bg\nJOIN bookings b ON bg.booking_id = b.booking_id\nJOIN children c ON bg.child_id = c.child_id\nJOIN baby_games g ON bg.game_id = g.id\nJOIN parents p ON b.parent_id = p.parent_id\nJOIN users u ON b.user_id = u.user_id\nJOIN events e ON b.event_id = e.id\nJOIN cities ci ON e.city_id = ci.id\nJOIN venues v ON e.venue_id = v.id\n\nORDER BY b.booking_id, c.child_id, g.id;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [600, 1420], "id": "1e02ff31-c929-4424-9309-72ee4772b50d", "name": "get all registration", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## Booking\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 640, "width": 2340, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [240, 1100], "typeVersion": 1, "id": "3ee49dab-1935-471b-a45a-39e39ce3076e", "name": "Sticky Note12"}, {"parameters": {"jsCode": "const user = $input.first().json;\nreturn [\n  {\n    json: {\n      object: user\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-640, 3120], "id": "db371673-5158-41f2-80e0-e7ae505e4e7f", "name": "Code3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ff843cfa-8ec9-4875-8231-89ef5fdc2447", "leftValue": "={{ $json.object }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-460, 3120], "id": "7e3f098b-484d-47a5-85ee-d5f48f2e6f94", "name": "If1", "alwaysOutputData": true}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/superadmin/login", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1020, 3120], "id": "1546e1c9-cea1-47c9-ad85-f08196df62a8", "name": "superadmin login", "webhookId": "616947f0-6717-4424-b9a5-c4b600768fed"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "employee", "mode": "list", "cachedResultName": "employee"}, "returnAll": true, "where": {"values": [{"column": "email", "value": "={{ $json.body.email }}"}, {"column": "password", "value": "={{ $json.body.password }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-840, 3120], "id": "f9662967-6e74-4263-ae80-d345aa67d780", "name": "superadmin employee login", "alwaysOutputData": true, "notesInFlow": false, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.success = false;\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-240, 3180], "id": "b3366528-cb9b-4452-8499-e947a793179e", "name": "False"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.success = true;\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-260, 3020], "id": "069f2e1a-ba5b-490b-aed9-8f30a6192172", "name": "true"}, {"parameters": {"content": "## promo code \n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 900, "width": 1660, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [80, 1780], "typeVersion": 1, "id": "df78ab92-4320-412e-87f9-1ff2bb27cd8d", "name": "Sticky Note13"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [180, 1940], "id": "e1f66963-9d4c-4630-9a4e-3136c71246e1", "name": "Promo code created", "webhookId": "818e0dac-975d-4a89-9a2c-c47763390531"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "promo_codes", "mode": "list", "cachedResultName": "promo_codes"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "value": "={{ $json.body.value }}", "usage_limit": "={{ $json.body.usage_limit }}", "usage_count": 0, "minimum_purchase_amount": "={{ $json.body.minimum_purchase_amount }}", "maximum_discount_amount": "={{ $json.body.maximum_discount_amount }}", "promo_code": "={{ $json.body.promo_code }}", "type": "={{ $json.body.type }}", "valid_from": "={{ $json.body.valid_from }}", "valid_to": "={{ $json.body.valid_to }}", "description": "={{ $json.body.description }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "promo_code", "displayName": "promo_code", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "type", "displayName": "type", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "value", "displayName": "value", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "valid_from", "displayName": "valid_from", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "valid_to", "displayName": "valid_to", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "usage_limit", "displayName": "usage_limit", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "usage_count", "displayName": "usage_count", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "minimum_purchase_amount", "displayName": "minimum_purchase_amount", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "maximum_discount_amount", "displayName": "maximum_discount_amount", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [360, 1940], "id": "aaa84bf5-2e7d-4d73-abb5-df78e8ced81b", "name": "create promo code", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const promoCodeId = $input.first().json.id;\nconst events = $('Promo code created').first().json.body.events || [];\n\nlet mappings = [];\nconst scope = $('Promo code created').first().json.body.scope;\nfor (const item of events) {\n  const eventId = item.id;\n  const gameIds = item.games_id || [];\n\n  for (const gameId of gameIds) {\n    if (eventId && gameId) {\n      mappings.push({\n        promocodetable_id: promoCodeId,\n        event_id: eventId,\n        game_id: gameId,\n        scope: scope\n      });\n    }\n  }\n}\n\nreturn mappings.map(m => ({ json: m }));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [520, 1940], "id": "fd2236de-0d44-470f-8714-3bb4b6b24a96", "name": "create promo"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO promo_code_mappings (\n  promocodetable_id,\n  event_id,\n  game_id,\n  scope\n) VALUES (\n  {{ $json.promocodetable_id }},\n  {{ $json.event_id }},\n  {{ $json.game_id }},\n  '{{ $json.scope }}'\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [700, 1940], "id": "77fc7b19-0cae-4bd3-8247-f948a66aa059", "name": "promo_code_mapping", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM promo_codes;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [380, 2140], "id": "6ae83f2b-c8da-4e3c-975e-8553625653af", "name": "get list of promo codes", "alwaysOutputData": false, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/promocode/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [180, 2140], "id": "81a7d11f-0e70-476f-a546-6e998ddb43c5", "name": "get all promocode", "webhookId": "036984f2-eeed-44f4-8923-73345f8a263b", "notesInFlow": false}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [180, 2320], "id": "07efc1b1-6177-41a7-8d8f-2bca835dc6cf", "name": "get single promo code", "webhookId": "9bb9a368-8750-474f-9da9-894b16712ffb"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [180, 2500], "id": "42611f67-2ec2-4bd3-9288-e2138dbb4283", "name": "del the single item", "webhookId": "7ec6d956-c374-43b0-9815-844e29c2a128"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT jsonb_build_object(\n  'promo_details', jsonb_build_object(\n    'id', pc.id,\n    'promo_code', pc.promo_code,\n    'type', pc.type,\n    'value', pc.value,\n    'valid_from', pc.valid_from,\n    'valid_to', pc.valid_to,\n    'usage_limit', pc.usage_limit,\n    'usage_count', pc.usage_count,\n    'minimum_purchase_amount', pc.minimum_purchase_amount,\n    'maximum_discount_amount', pc.maximum_discount_amount,\n    'description', pc.description,\n    'is_active', pc.is_active,\n    'created_at', pc.created_at,\n    'updated_at', pc.updated_at\n  ),\n  'events', COALESCE(\n    jsonb_agg(DISTINCT jsonb_build_object(\n      'event_details', jsonb_build_object(\n        'id', e.id,\n        'title', e.title,\n        'event_date', e.event_date,\n        'status', e.status,\n        'city_id', e.city_id,\n        'venue_id', e.venue_id\n      ),\n      'games', (\n        SELECT jsonb_agg(DISTINCT jsonb_build_object(\n          'id', bg.id,\n          'game_name', bg.game_name,\n          'min_age', bg.min_age,\n          'max_age', bg.max_age,\n          'duration_minutes', bg.duration_minutes,\n          'categories', bg.categories,\n          'is_active', bg.is_active\n        ))\n        FROM baby_games bg\n        JOIN promo_code_mappings pcm2\n          ON bg.id = pcm2.game_id\n        WHERE pcm2.event_id = e.id\n          AND pcm2.promocodetable_id = pc.id\n      )\n    )),\n    '[]'::jsonb\n  )\n) AS promo_data\nFROM promo_codes pc\nLEFT JOIN promo_code_mappings pcm ON pc.id = pcm.promocodetable_id\nLEFT JOIN events e ON pcm.event_id = e.id\nWHERE pc.id = {{ $json.body.id }} -- or $json.id based on n8n context\nGROUP BY pc.id;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [380, 2320], "id": "a3982bc5-2a6e-4cc9-82a7-9b85415c19c2", "name": "get single item", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- First delete mappings\nDELETE FROM promo_code_mappings\nWHERE promocodetable_id = {{ $json.body.id }};  -- Replace with your actual promo code ID\n\n-- Then delete the promo code itself\nDELETE FROM promo_codes\nWHERE id = {{ $json.body.id }};  -- Replace with your actual promo code ID\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [380, 2500], "id": "0d0519c0-7ea8-4864-870c-b0b6bedfff7c", "name": "del promo code", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const promoCodeId = $('promocode update').first().json.body.id;\nconst events = $('promocode update').first().json.body.events || [];\n\nlet mappings = [];\nconst scope =$('promocode update').first().json.body.scope;\nfor (const item of events) {\n  const eventId = item.id;\n  const gameIds = item.games_id || [];\n\n  for (const gameId of gameIds) {\n    if (eventId && gameId) {\n      mappings.push({\n        promocodetable_id: promoCodeId,\n        event_id: eventId,\n        game_id: gameId,\n        scope: scope\n      });\n    }\n  }\n}\n\nreturn mappings.map(m => ({ json: m }));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1180, 2140], "id": "4dbd88de-6f75-4a6e-9d40-7c59bbd7d3d2", "name": "Code4"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO promo_code_mappings (\n  promocodetable_id,\n  event_id,\n  game_id,\n  scope\n) VALUES (\n  {{ $json.promocodetable_id }},\n  {{ $json.event_id }},\n  {{ $json.game_id }},\n  '{{ $json.scope }}'\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1340, 2140], "id": "92f3132a-8506-4683-a3f2-e15e94aef0ad", "name": "Postgres4", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## Testimonials\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 700, "width": 1080, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [60, 2740], "typeVersion": 1, "id": "6fdfac33-1220-496e-a8e2-680de6bcd79a", "name": "Sticky Note14"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [640, 2140], "id": "35fba7c4-470c-46c4-8667-0a55a2c70282", "name": "promocode update", "webhookId": "fe294521-e5aa-413a-9ce1-b1cc76a36ff3"}, {"parameters": {"operation": "upsert", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "promo_codes", "mode": "list", "cachedResultName": "promo_codes"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": "={{ $json.body.is_active }}", "id": "={{ $json.body.id }}", "value": "={{ $json.body.value }}", "usage_limit": "={{ $json.body.usage_limit }}", "usage_count": "=", "minimum_purchase_amount": "={{ $json.body.minimum_purchase_amount }}", "maximum_discount_amount": "={{ $json.body.maximum_discount_amount }}", "type": "={{ $json.body.type }}", "promo_code": "={{ $json.body.promo_code }}", "valid_from": "={{ $json.body.valid_from }}", "valid_to": "={{ $json.body.valid_to }}", "description": "={{ $json.body.description }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "promo_code", "displayName": "promo_code", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "type", "displayName": "type", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "value", "displayName": "value", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": false}, {"id": "valid_from", "displayName": "valid_from", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "valid_to", "displayName": "valid_to", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "usage_limit", "displayName": "usage_limit", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": false}, {"id": "usage_count", "displayName": "usage_count", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": false}, {"id": "minimum_purchase_amount", "displayName": "minimum_purchase_amount", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": false}, {"id": "maximum_discount_amount", "displayName": "maximum_discount_amount", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": false}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": false}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": false}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [820, 2140], "id": "b9359278-3bf5-4d6f-ac61-86315fb007f4", "name": "Update promocode", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM promo_code_mappings\nWHERE promocodetable_id = {{ $('promocode update').item.json.body.id }}  ;  -- Replace with your actual promo code ID\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1000, 2140], "id": "df3a9baa-097e-4f76-9955-a49a7208f1ff", "name": "Delete mappings", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/testimonials/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [160, 2880], "id": "81a5c675-b719-4299-9ab3-9b6049807ddf", "name": "Testimonials create", "webhookId": "16b9a990-0dbd-478d-8c5d-922b517408f0"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "testimonials", "mode": "list", "cachedResultName": "testimonials"}, "columns": {"mappingMode": "defineBelow", "value": {"event_id": "={{ $json.body.event_id }}", "rating": "={{ $json.body.rating }}", "name": "={{ $json.body.name }}", "city": "={{ $json.body.city_id }}", "testimonial": "={{ $json.body.testimonial }}", "status": "={{ $json.body.status }}", "submitted_at": "={{ $json.body.date }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city", "displayName": "city", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "event_id", "displayName": "event_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "testimonial", "displayName": "testimonial", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "submitted_at", "displayName": "submitted_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [360, 2880], "id": "a07222d4-74b2-469a-983f-daeb29e65e2a", "name": "Create testimonial", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/testimonials/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [160, 3080], "id": "d393320a-c1bc-459b-94b4-2a7d15649d01", "name": "Get single testimonial", "webhookId": "1f74deaa-f800-4716-9207-7be505bb5c11"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "testimonials", "mode": "list", "cachedResultName": "testimonials"}, "limit": 1, "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [380, 3080], "id": "a623b3d1-97a1-48e8-a731-72153f45318b", "name": "get testimonial by id", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/testimonials/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [180, 3280], "id": "ff07c92d-798b-4a82-9f98-14b1b0b009b4", "name": "Get-all testimonials", "webhookId": "47a43070-1c07-4839-8772-10811adb167b"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM testimonials\nORDER BY submitted_at DESC;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [380, 3280], "id": "81c93f2b-a794-4aef-af6a-52a6622aea22", "name": "Postgres1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/testimonials/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [620, 2900], "id": "d78fecca-b1a9-4c86-82f8-c3da1f52a3f9", "name": "Testimonial update", "webhookId": "01d84e75-899c-41d8-92fe-1e75e0ff7898"}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "testimonials", "mode": "list", "cachedResultName": "testimonials"}, "columns": {"mappingMode": "defineBelow", "value": {"name": "={{ $json.body.name }}", "city": "={{ $json.body.city }}", "event_id": "={{ $json.body.event_id }}", "rating": "={{ $json.body.rating }}", "testimonial": "={{ $json.body.testimonial }}", "submitted_at": "={{ $json.body.date }}", "status": "={{ $json.body.status }}", "id": "={{ $json.body.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "city", "displayName": "city", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "event_id", "displayName": "event_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "testimonial", "displayName": "testimonial", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "submitted_at", "displayName": "submitted_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [840, 2900], "id": "6e231a56-08f3-49cf-a9bf-4280841c145e", "name": "Testimonial update1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/testimonials/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [620, 3100], "id": "4bb98085-5d55-4f52-947f-dc9399a089e9", "name": "Testimonial delete", "webhookId": "47b20135-78d4-403b-b8c5-4936898bae51"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "testimonials", "mode": "list", "cachedResultName": "testimonials"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [840, 3100], "id": "b8c9d825-3f51-4365-acd7-6a9f9e1a9852", "name": "Delete testimonial", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "Add-ons\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 940, "width": 1260, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [-2380, 3300], "typeVersion": 1, "id": "482ba5ab-de3d-4d93-9e54-72523f1b1fcd", "name": "Sticky Note15"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/addons/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2340, 3400], "id": "783d3c38-414f-4413-8ed7-4e9495ac5612", "name": "Add-ons create", "webhookId": "82c95f5a-6ed7-4bf7-b62d-79b215de66a2"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "addons", "mode": "list", "cachedResultName": "addons"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "has_variants": false, "name": "={{ $json.body.name }}", "description": "={{ $json.body.description }}", "price": "={{ $json.body.price }}", "category": "={{ $json.body.category }}", "stock_quantity": "={{ $json.body.stock_quantity }}", "sku": "={{ $json.body.sku }}", "bundle_min_quantity": "={{ $json.body.bundleDiscount.minQuantity }}", "bundle_discount_percentage": "={{ $json.body.bundleDiscount.discountPercentage }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "price", "displayName": "price", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "has_variants", "displayName": "has_variants", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "stock_quantity", "displayName": "stock_quantity", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "sku", "displayName": "sku", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "bundle_min_quantity", "displayName": "bundle_min_quantity", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "bundle_discount_percentage", "displayName": "bundle_discount_percentage", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2180, 3400], "id": "73a98f56-aeb6-4c62-b6d9-e67f580f7fb6", "name": "Insert Add-on", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const addonId = $input.first().json.id;\nconst images = $('Add-ons create').first().json.body.images;\n\nreturn images.map(imageUrl => {\n  return {\n    addon_id: addonId,\n    image_url: imageUrl\n  };\n});\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2020, 3400], "id": "e81dceb3-f7d4-4cdf-8fcb-56c92edf49a8", "name": "Code5"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO addon_images (addon_id, image_url)\nVALUES ({{$json[\"addon_id\"]}}, '{{$json[\"image_url\"]}}');\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1860, 3400], "id": "a85b0719-80b6-4454-88aa-e06f679a8f05", "name": "Insert Images", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Add-ons create').item.json.body.hasVariants }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "9bdc1161-2b1b-45fa-bce5-2da78b73fe63"}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1720, 3400], "id": "13583d44-40ea-4836-98e7-f5eb8d97b78e", "name": "Has<PERSON><PERSON><PERSON>"}, {"parameters": {"jsCode": "const addonId = $('Insert Add-on').first().json.id;\nreturn $('Add-ons create').first().json.body.variants.map(v => ({\n  addon_id: addonId,\n  name: v.name,\n  price_modifier: v.price_modifier || 0,\n  sku: v.sku,\n  stock_quantity: v.stock_quantity\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1560, 3400], "id": "8ef93a65-c778-40e3-adda-a16c8c4384d5", "name": "Insert Variants"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/addons/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1960, 4020], "id": "de458653-2b1f-477b-afff-04dd5413a733", "name": "Get single Add-on", "webhookId": "9ad07820-5978-4b5a-a8ce-5eb5ce7c385a"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  a.id,\n  a.name,\n  a.description,\n  a.price,\n  a.category,\n  a.is_active,\n  a.has_variants,\n  a.stock_quantity,\n  a.sku,\n  a.bundle_min_quantity,\n  a.bundle_discount_percentage,\n\n  COALESCE(\n    ARRAY_AGG(DISTINCT ai.image_url) FILTER (WHERE ai.image_url IS NOT NULL),\n    '{}'\n  ) AS images,\n\n  COALESCE(\n    JSON_AGG(DISTINCT av) FILTER (WHERE av.id IS NOT NULL),\n    '[]'\n  ) AS variants\n\nFROM addons a\nLEFT JOIN addon_images ai ON ai.addon_id = a.id\nLEFT JOIN addon_variants av ON av.addon_id = a.id\n\nWHERE a.id = {{ $json.body.id }}\n\nGROUP BY\n  a.id, a.name, a.description, a.price, a.category,\n  a.is_active, a.has_variants, a.stock_quantity,\n  a.sku, a.bundle_min_quantity, a.bundle_discount_percentage;\n\n\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1800, 4000], "id": "492e9bcb-f807-4f62-9031-7ca4d46fafc2", "name": "Postgres5", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "addon_variants", "mode": "list", "cachedResultName": "addon_variants"}, "columns": {"mappingMode": "defineBelow", "value": {"addon_id": "={{ $json.addon_id }}", "name": "={{ $json.name }}", "price_modifier": "={{ $json.price_modifier }}", "sku": "={{ $json.sku }}", "stock_quantity": "={{ $json.stock_quantity }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "addon_id", "displayName": "addon_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "price_modifier", "displayName": "price_modifier", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "sku", "displayName": "sku", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "stock_quantity", "displayName": "stock_quantity", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1400, 3400], "id": "f6a36e39-65eb-4240-bdc8-8df0b70a3d89", "name": "Insert into Variants", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/addons/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2340, 3780], "id": "e1e5ff52-1e46-40bb-a35e-4dd94eebdb9d", "name": "Get-all Add-ons", "webhookId": "6004e5de-e7a7-4072-b2c0-91f34c99d568"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  a.id,\n  a.name,\n  a.description,\n  a.price,\n  a.category,\n  a.is_active,\n  a.has_variants,\n  a.stock_quantity,\n  a.sku,\n  a.bundle_min_quantity,\n  a.bundle_discount_percentage,\n  COALESCE(ARRAY_AGG(DISTINCT ai.image_url) FILTER (WHERE ai.image_url IS NOT NULL), '{}') AS images,\n  COALESCE(\n    JSON_AGG(DISTINCT av) FILTER (WHERE av.id IS NOT NULL), \n    '[]'\n  ) AS variants\nFROM addons a\nLEFT JOIN addon_images ai ON ai.addon_id = a.id\nLEFT JOIN addon_variants av ON av.addon_id = a.id\nGROUP BY \n  a.id, a.name, a.description, a.price, a.category, \n  a.is_active, a.has_variants, a.stock_quantity, \n  a.sku, a.bundle_min_quantity, a.bundle_discount_percentage\nORDER BY a.id DESC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2160, 3780], "id": "bf599d91-f9cc-4d15-839e-cc796705f301", "name": "Get-all", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/addons/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2360, 3580], "id": "35cace46-94ac-4232-9cda-59b7ad889cd6", "name": "Update", "webhookId": "37f339ba-e538-4506-bc67-d1a2943d6d23"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE addons\nSET \n  name = '{{ $json.body.name }}',\n  description = '{{ $json.body.description }}',\n  price = {{ $json.body.price }},\n  category = '{{ $json.body.category }}',\n  is_active = {{ $json.body.isActive }} ,\n  has_variants = {{ $json.body.hasVariants }},\n  stock_quantity = {{ $json.body.stockQuantity }},\n  sku = '{{ $json.body.sku }}',\n  bundle_min_quantity = {{ $json.body.bundleDiscount.minQuantity }},\n  bundle_discount_percentage = {{ $json.body.bundleDiscount.discountPercentage }},\n  updated_at = NOW()\nWHERE id = {{ $json.body.id }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2240, 3580], "id": "b537b215-e68a-432a-a230-c42320ce366f", "name": "Update Add-on", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM addon_images WHERE addon_id = {{ $('Update').item.json.body.id }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2080, 3580], "id": "483d00e1-070f-45ba-95d6-088c32bb1894", "name": "Delete Add-on images", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const addonId = $('Update').first().json.body.id;\nreturn $('Update').first().json.body.images.map(url => ({\n  addon_id: addonId,\n  image_url: url\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1900, 3580], "id": "4db0bb58-c9b5-480a-a328-cd2d67640b08", "name": "Insert Add-on images"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM addon_variants WHERE addon_id = {{ $('Update').item.json.body.id }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1580, 3580], "id": "0fbd3dbf-1704-457f-a6e0-26702ade2250", "name": "Delete variants", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$('Update').item.json.body.hasVariants  === true }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "f9d08849-e171-4b0e-aa53-506a35353c61"}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1440, 3580], "id": "9c8080f3-1fbc-4fb9-acbf-9f6d122bba26", "name": "Switch"}, {"parameters": {"jsCode": "const addonId = $('Update').first().json.body.id;\nreturn $('Update').first().json.body.variants.map(v => ({\n  addon_id: addonId,\n  name: v.name,\n  price_modifier: v.price_modifier || 0,\n  sku: v.sku,\n  stock_quantity: v.stock_quantity\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1840, 3800], "id": "********-87a2-4a16-9aea-7980e0ac6336", "name": "Insert variants"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/addons/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1540, 4000], "id": "d0d90e28-2c78-4fd9-910c-a98ece6e047c", "name": "Delete Add-on", "webhookId": "e22d29ac-c2f5-45d1-8c76-8d8a96afa0dd"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM addons WHERE id = {{ $json.body.id }};", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1380, 3960], "id": "e052fe3f-b175-4c5b-a83c-aa0be6e228f6", "name": "Postgres3", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "Payment\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 800, "width": 960, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-1080, 3380], "typeVersion": 1, "id": "9c625883-46d8-42de-bd17-28d938e0bbd9", "name": "Sticky Note16"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/payments/create", "responseMode": "lastNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1040, 3460], "id": "4b015d73-846f-4570-a278-028f3db7f1cd", "name": "Payment-create", "webhookId": "3db0ea48-eb45-44a4-a4cd-bb10c96422e6"}, {"parameters": {"jsCode": "// Get the input data from webhook\nconst inputData = $input.first().json.body;\n\n// Validate required fields\nconst requiredFields = ['booking_id', 'transaction_id', 'amount', 'payment_status'];\nconst missingFields = [];\n\nfor (const field of requiredFields) {\n  if (!inputData[field]) {\n    missingFields.push(field);\n  }\n}\n\nif (missingFields.length > 0) {\n  return [{\n    json: {\n      success: false,\n      error: `Missing required fields: ${missingFields.join(', ')}`\n    }\n  }];\n}\n\n// Validate payment_status\nconst validStatuses = ['successful', 'pending', 'failed', 'refunded'];\nif (!validStatuses.includes(inputData.payment_status)) {\n  return [{\n    json: {\n      success: false,\n      error: `Invalid payment_status. Must be one of: ${validStatuses.join(', ')}`\n    }\n  }];\n}\n\n// Prepare data for database insertion\nreturn [{\n  json: {\n    ...inputData,\n    gateway_response: JSON.stringify(inputData.gateway_response || {}),\n    payment_date: inputData.payment_date || new Date().toISOString(),\n    payment_method: inputData.payment_method || 'PhonePe'\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-840, 3460], "id": "d63e544b-f3d9-468b-b93a-a7f3ab3b3c09", "name": "Validate input"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "payments", "mode": "list", "cachedResultName": "payments"}, "columns": {"mappingMode": "defineBelow", "value": {"booking_id": "={{ $json.booking_id }}", "transaction_id": "={{ $json.transaction_id }}", "phonepe_transaction_id": "={{ $json.phonepe_transaction_id }}", "amount": "={{ $json.amount }}", "payment_method": "={{ $json.payment_method }}", "payment_status": "={{ $json.payment_status }}", "gateway_response": "={{ $json.gateway_response }}", "payment_date": "={{ $json.payment_date }}"}, "matchingColumns": ["payment_id"], "schema": [{"id": "payment_id", "displayName": "payment_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "booking_id", "displayName": "booking_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "transaction_id", "displayName": "transaction_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "phonepe_transaction_id", "displayName": "phonepe_transaction_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "amount", "displayName": "amount", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "payment_method", "displayName": "payment_method", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "payment_status", "displayName": "payment_status", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "payment_date", "displayName": "payment_date", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "gateway_response", "displayName": "gateway_response", "required": false, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}, {"id": "refund_amount", "displayName": "refund_amount", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "refund_date", "displayName": "refund_date", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "refund_reason", "displayName": "refund_reason", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "admin_notes", "displayName": "admin_notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-660, 3460], "id": "9ea32a8b-ef2e-4188-a539-6360cab66694", "name": "Insert payments", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "return [{\n  json: {\n    success: true,\n    message: \"Payment created successfully\"\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-460, 3460], "id": "5a709fb0-bfa0-4844-9cd7-a5291d7a72b3", "name": "Response"}, {"parameters": {"path": "v1/nibog/payments/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1040, 3620], "id": "1108eaa9-83bd-456f-b4fc-123f6367c50d", "name": "Get all payments", "webhookId": "180ee63c-8de6-4c62-ba0b-ed9272c07a1e"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  p.payment_id,\n  p.booking_id,\n  p.transaction_id,\n  p.amount,\n  p.payment_method,\n  p.payment_status,\n  p.payment_date,\n  p.created_at,\n  \n  -- User details\n  u.full_name as user_name,\n  \n  -- Event details  \n  e.title as event_title,\n  e.event_date,\n  \n  -- City details\n  c.city_name,\n  \n  -- Venue details\n  v.venue_name\n\nFROM payments p\nJOIN bookings b ON p.booking_id = b.booking_id\nJOIN users u ON b.user_id = u.user_id\nJOIN events e ON b.event_id = e.id\nJOIN cities c ON e.city_id = c.id\nJOIN venues v ON e.venue_id = v.id\n\nORDER BY p.created_at DESC\nLIMIT 100;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-820, 3620], "id": "57b2e930-c967-4cec-b36c-98481e746538", "name": "Get-all-payments", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/payments/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1040, 3780], "id": "d4c8ce91-8cf2-4bfb-941b-b7b6a7ccb5d5", "name": "Get-single-payment", "webhookId": "f622f387-02bf-4b4f-9131-c765d2633a3d"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  p.*,\n  u.full_name as user_name,\n  u.email as user_email,\n  u.phone as user_phone,\n  e.title as event_title,\n  e.event_date,\n  e.description as event_description,\n  c.city_name,\n  v.venue_name,\n  v.address as venue_address,\n  b.booking_ref,\n  b.status as booking_status,\n  b.total_amount as booking_total_amount\n\nFROM payments p\nJOIN bookings b ON p.booking_id = b.booking_id\nJOIN users u ON b.user_id = u.user_id\nJOIN events e ON b.event_id = e.id\nJOIN cities c ON e.city_id = c.id\nJOIN venues v ON e.venue_id = v.id\n\nWHERE p.payment_id = {{ $json.body.id }};", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-820, 3780], "id": "93dc8ae3-bcdb-48b9-8455-42de505060b3", "name": "single-payment", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/payments/analytics", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-600, 3620], "id": "e8805461-c26a-456d-983f-05693608813a", "name": "get-analytics", "webhookId": "b2712deb-67be-41e3-8198-972038014a35"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH monthly_stats AS (\n  SELECT\n    TO_CHAR(DATE_TRUNC('month', payment_date), 'YYYY-MM') AS month,\n    SUM(CASE WHEN payment_status = 'successful' THEN amount ELSE 0 END) AS revenue,\n    COUNT(*) AS count\n  FROM payments\n  WHERE payment_date >= CURRENT_DATE - INTERVAL '6 months'\n  GROUP BY DATE_TRUNC('month', payment_date)\n)\n\nSELECT\n  -- Summary statistics\n  COUNT(*) AS total_payments,\n  SUM(CASE WHEN payment_status = 'successful' THEN amount ELSE 0 END) AS total_revenue,\n  COUNT(CASE WHEN payment_status = 'successful' THEN 1 END) AS successful_payments,\n  COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) AS pending_payments,\n  COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) AS failed_payments,\n  COUNT(CASE WHEN payment_status = 'refunded' THEN 1 END) AS refunded_payments,\n  ROUND(AVG(CASE WHEN payment_status = 'successful' THEN amount END)::numeric, 2)::float AS average_transaction,\n\n  -- Monthly data as JSON array\n  (SELECT json_agg(monthly_stats) FROM monthly_stats) AS monthly_data\n\nFROM payments\nWHERE payment_date >= CURRENT_DATE - INTERVAL '1 year';\n\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-380, 3620], "id": "2b08d01e-bdfb-4372-82d5-510760f2e39a", "name": "get analytics", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/payments/export", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1000, 4000], "id": "3c33465e-9257-4b6b-8046-472ad598ae12", "name": "Export", "webhookId": "61609fef-a12a-498c-ad09-8b019990cc39"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/payments/update-status", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-600, 3780], "id": "7a2d9c3f-a4ac-4ff7-a3be-9decaf2d289b", "name": "Update status", "webhookId": "9d7d6c2a-dda2-4d59-9994-2898451041ba"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH validation AS (\n  SELECT \n    payment_id, \n    payment_status, \n    amount,\n    CASE \n      WHEN payment_status = 'successful' THEN true\n      ELSE false \n    END AS is_valid_update\n  FROM payments\n  WHERE payment_id = {{ $json.body.payment_id }}\n)\nUPDATE payments\nSET \n  payment_status = CASE WHEN v.is_valid_update THEN '{{ $json.body.status }}' ELSE payments.payment_status END,\n  refund_amount = CASE \n    WHEN v.is_valid_update THEN COALESCE({{ $json.body.refund_amount }}::DECIMAL, v.amount) \n    ELSE payments.refund_amount \n  END,\n  refund_date = CASE \n    WHEN v.is_valid_update THEN NOW() \n    ELSE payments.refund_date \n  END,\n  refund_reason = CASE \n    WHEN v.is_valid_update THEN '{{ $json.body.refund_reason }}' \n    ELSE payments.refund_reason \n  END,\n  admin_notes = COALESCE('Full refund processed due to event cancellation', payments.admin_notes),\n  updated_at = NOW()\nFROM validation v\nWHERE payments.payment_id = v.payment_id\nRETURNING \n  payments.payment_id,\n  payments.payment_status,\n  payments.refund_amount,\n  payments.refund_date,\n  payments.updated_at,\n  v.is_valid_update,\n  CASE \n    WHEN NOT v.is_valid_update \n    THEN 'Error: Cannot refund payment with status: ' || v.payment_status \n    ELSE 'Success: Payment refunded' \n  END AS message;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-400, 3780], "id": "b900acc6-2305-4519-bef3-df6f2c491e55", "name": "status-update", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  'P' || LPAD(p.payment_id::TEXT, 3, '0') AS payment_id,\n  'B' || LPAD(p.booking_id::TEXT, 3, '0') AS booking_id,\n  u.full_name AS user_name,\n  u.email AS user_email,\n  u.phone AS user_phone,\n  e.title AS event_title,\n  e.event_date,\n  c.city_name,\n  v.venue_name,\n  p.amount,\n  p.payment_method,\n  p.payment_status,\n  p.transaction_id,\n  p.payment_date,\n  COALESCE(p.refund_amount, 0) AS refund_amount,\n  p.refund_reason\nFROM payments p\nJOIN bookings b ON p.booking_id = b.booking_id\nJOIN users u ON b.user_id = u.user_id\nJOIN events e ON b.event_id = e.id\nJOIN cities c ON e.city_id = c.id\nJOIN venues v ON e.venue_id = v.id\nWHERE 1=1\n  AND (\n    '{{ $json.query.status }}' IS NULL \n    OR '{{ $json.query.status }}' = '' \n    OR LOWER('{{ $json.query.status }}') = 'all'\n    OR p.payment_status = '{{ $json.query.status }}'\n  )\n  AND (\n    '{{ $json.query.start_date }}' IS NULL \n    OR '{{ $json.query.start_date }}' = '' \n    OR DATE(p.payment_date) >= '{{ $json.query.start_date }}'::DATE\n  )\n  AND (\n    '{{ $json.query.end_date }}' IS NULL \n    OR '{{ $json.query.end_date }}' = '' \n    OR DATE(p.payment_date) <= '{{ $json.query.end_date }}'::DATE\n  )\nORDER BY p.created_at DESC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-800, 4000], "id": "f74ef8e2-e6a7-4bd8-a5b7-fe8a689766dd", "name": "Export payment list", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-580, 4000], "id": "d1726ebc-0306-4b01-ab0a-6620fa620281", "name": "Convert to File"}, {"parameters": {"respondWith": "binary", "responseDataSource": "set", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [-360, 4000], "id": "7f7e6db1-1d57-4294-9ec7-9be4ead8e3d3", "name": "Respond to Webhook"}, {"parameters": {"content": "Certificate Apis\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 1220, "width": 1240, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-3320, 880], "typeVersion": 1, "id": "d44346a2-237e-4160-99c0-6a22b874c1de", "name": "Sticky Note17"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/certificate-templates/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3240, 980], "id": "5311bb29-fc83-43bf-ab2c-799bbadd3d7a", "name": "Certificate-template", "webhookId": "3a1b28a1-51e4-4170-a762-bfb3d2dc1c47"}, {"parameters": {"jsCode": "return [{\n  json: {\n    name: $input.first().json.body.name,\n    description: $input.first().json.body.description,\n    type: $input.first().json.body.type,\n    certificate_title:       $input.first().json.body.certificate_title,\n    background_image: $input.first().json.body.background_image || null,\n    background_style: $input.first().json.body.background_style || null,\n    paper_size: $input.first().json.body.paper_size || 'a4',\n    orientation: $input.first().json.body.orientation || 'landscape',\n    fields: $input.first().json.body.fields,\n    appreciation_text: $input.first().json.body.appreciation_text || null,\n    appreciation_text_style: $input.first().json.body.appreciation_text_style || null,\n    signature_image: $input.first().json.body.signature_image || null,\n    is_active: true,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString()\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3040, 980], "id": "28b40f1a-5d81-49db-8328-47cceab0b22b", "name": "Insert template"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO certificate_templates (\n  name,\n  description,\n  type,\n  certificate_title,\n  background_image,\n  background_style,\n  paper_size,\n  orientation,\n  fields,\n  appreciation_text,\n  appreciation_text_style,\n  signature_image,\n  is_active,\n  created_at,\n  updated_at\n) VALUES (\n  '{{ $json.name }}',\n  '{{ $json.description }}',\n  '{{ $json.type }}',\n  '{{ $json.certificate_title }}',\n  '{{ $json.background_image }}',\n  '{{ JSON.stringify($json.background_style) }}'::JSONB,\n  '{{ $json.paper_size }}',\n  '{{ $json.orientation }}',\n  '{{ JSON.stringify($json.fields) }}'::JSONB,\n  '{{ $json.appreciation_text }}',\n  '{{ JSON.stringify($json.appreciation_text_style) }}'::JSONB,\n  '{{ $json.signature_image }}',\n  {{ $json.is_active }},\n  '{{ $json.created_at }}',\n  '{{ $json.updated_at }}'\n)\nRETURNING *;\n\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2860, 980], "id": "1a985100-da9d-44f4-be49-8a5cf5d9796e", "name": "Insert-template", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/certificate-templates/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3220, 1160], "id": "9a528e23-d292-4c1d-a4fc-7b71d1670ef1", "name": "Get-all Templates", "webhookId": "d11bd6d5-95e8-4403-ab04-7b742b00266d"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.certificate_templates\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-3000, 1160], "id": "cab670ed-d782-41b0-857e-0d1515641094", "name": "Get-all1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/certificate-templates/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2720, 1160], "id": "b7246e31-0969-4ec7-8d7d-8f4593a26956", "name": "Get-single template", "webhookId": "fc197929-75ba-471f-9989-f7ffb18058c2"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.certificate_templates WHERE ID = {{ $json.body.id }}\nORDER BY id ASC ", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2500, 1160], "id": "239a7432-4fa9-4269-a1b7-32874c07fbf9", "name": "Get-single", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/certificate-templates/update", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3280, 1340], "id": "33905fb4-b711-44c0-8aa0-ba95093622cc", "name": "Update Template", "webhookId": "0e0255bc-7c88-4b2a-b760-79a95419c0be"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE certificate_templates\nSET\n  name = '{{ $json.body.name }}',\n  description = '{{ $json.body.description }}',\n  type = '{{ $json.body.type }}',\n  certificate_title = '{{ $json.body.certificate_title }}',\n  background_image = '{{ $json.body.background_image }}',\n  background_style = '{{ JSON.stringify($json.body.background_style) }}'::jsonb,\n  paper_size = '{{ $json.body.paper_size || \"a4\" }}',\n  orientation = '{{ $json.body.orientation || \"landscape\" }}',\n  fields = '{{ JSON.stringify($json.body.fields) }}'::jsonb,\n  appreciation_text = '{{ $json.body.appreciation_text }}',\n  appreciation_text_style = '{{ JSON.stringify($json.body.appreciation_text_style) }}'::jsonb,\n  signature_image = '{{ $json.body.signature_image }}',\n  is_active = {{ $json.body.is_active || true }},\n  updated_at = CURRENT_TIMESTAMP\nWHERE id = {{ $json.body.id }};\n\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-3100, 1340], "id": "fc912b36-590d-49dc-b94e-e69de6366138", "name": "Update-template", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/certificate-templates/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2880, 1340], "id": "15f03103-a213-41bc-aefd-2bf3891ac67b", "name": "Delete Template", "webhookId": "d269e8f3-88e5-439c-a243-3d589a2c9965"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "certificate_templates", "mode": "list", "cachedResultName": "certificate_templates"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2680, 1340], "id": "bfbcdebf-33ea-41ba-871c-cdefdbbb086a", "name": "Delete-template", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/certificate-templates/by-type", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2460, 1340], "id": "6856d684-b826-4e01-bcf3-c78cec9d46a0", "name": "Get by type", "webhookId": "2dd5df5e-9384-442a-90ed-9387dab36c09"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "certificate_templates", "mode": "list", "cachedResultName": "certificate_templates"}, "returnAll": true, "where": {"values": [{"column": "type", "value": "={{ $json.body.type }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2260, 1340], "id": "b32e3a3f-b372-4c04-8d0b-b5c6b79a023d", "name": "return-by-type", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/certificate/generate-single", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3260, 1540], "id": "024145b2-bd78-4732-8b6a-f3e60c8c0501", "name": "Generate-certificate", "webhookId": "9e776a49-8381-4f57-ab61-4a5a79d52ab7"}, {"parameters": {"jsCode": "const body = $input.first().json.body;\n\nif (!body.template_id || !body.event_id || !body.user_id || !body.certificate_data) {\n  throw new Error('Missing required fields');\n}\n\nreturn {\n  json: body\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3080, 1540], "id": "42e1c47a-8de1-461e-8a1f-bc1d196d9aec", "name": "validate"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM certificate_templates \nWHERE id = {{ $('validate').item.json.template_id }} AND is_active = true;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2760, 1540], "id": "7eb36136-4477-4982-bcbe-a0818f12c9fc", "name": "Get Template", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT e.*, v.venue_name, c.city_name \nFROM events e \nJOIN venues v ON e.venue_id = v.id \nJOIN cities c ON v.city_id = c.id \nWHERE e.id = {{ $('Generate-certificate').item.json.body.event_id }};", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2620, 1540], "id": "1db01bcb-6c3f-495d-b2f0-1d873e16a47f", "name": "Get Event", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const inputData = $input.all()[0].json;\nconst userData = $('Validate user').first().json; // From the new lookup step\n\nconst certificateNumber = `CERT-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;\n\nconst enhancedCertificateData = {\n  ...inputData.certificate_data,\n  certificate_number: certificateNumber,\n  event_name: $input.first().json.title,\n  venue_name: $input.first().json.venue_name,\n  city_name: $input.first().json.city_name\n};\n\nreturn {\n  json: {\n    template_id: inputData.template_id,\n    event_id: inputData.event_id,\n    game_id: inputData.game_id,\n    user_id: userData.user_id, // Use the looked up user_id\n    child_id: inputData.child_id,\n    certificate_data: JSON.stringify(enhancedCertificateData),\n    pdf_url: null,\n    status: 'generated'\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2460, 1540], "id": "d47415a9-638d-426c-8c2b-55627a10809d", "name": "Generate-Data"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "generated_certificates", "mode": "list", "cachedResultName": "generated_certificates"}, "columns": {"mappingMode": "defineBelow", "value": {"template_id": "={{ $('Get Template').item.json.id }}", "event_id": "={{ $('Get Event').item.json.id }}", "game_id": "={{ $('validate').item.json.game_id }}", "user_id": "={{ $('Validate user').item.json.user_id }}", "child_id": "={{ $('validate').item.json.child_id }}", "certificate_data": "={{ $json.certificate_data }}", "pdf_url": "={{ $json.pdf_url }}", "status": "={{ $json.status }}", "parent_email": "={{ $('Validate user').item.json.parent_name }}", "parent_name": "={{ $('Validate user').item.json.email }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "template_id", "displayName": "template_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "event_id", "displayName": "event_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "game_id", "displayName": "game_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "child_id", "displayName": "child_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "certificate_data", "displayName": "certificate_data", "required": true, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}, {"id": "pdf_url", "displayName": "pdf_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "generated_at", "displayName": "generated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "sent_at", "displayName": "sent_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "downloaded_at", "displayName": "downloaded_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "parent_email", "displayName": "parent_email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "parent_name", "displayName": "parent_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2300, 1540], "id": "35284c2a-12c9-4b93-a0d0-973034de4f18", "name": "Generate", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/certificate/download", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3280, 1720], "id": "5cc7b410-dbc3-491c-866c-69a6a0f288c1", "name": "Download-pdf", "webhookId": "4918c1f2-9a34-4817-a88d-791266653e5f"}, {"parameters": {"jsCode": "const certificateId = $input.first().json.query.certificate_id;\n\nif (!certificateId || isNaN(certificateId)) {\n  throw new Error('Valid Certificate ID is required');\n}\n\nreturn {\n  json: {\n    certificate_id: parseInt(certificateId)\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3120, 1720], "id": "471b5698-ac0a-48ec-af9f-64f55304f49c", "name": "Extract-id"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  gc.id,\n  gc.certificate_data,\n  gc.pdf_url,\n  gc.status,\n  gc.generated_at,\n  ct.name as template_name,\n  ct.background_image,\n  ct.fields,\n  ct.paper_size,\n  ct.orientation,\n  u.full_name as user_name,\n  u.email as user_email,\n  c.full_name as child_name,\n  e.title as event_name,\n  e.event_date,\n  v.venue_name as venue_name,\n  city.city_name as city_name\nFROM generated_certificates gc\nJOIN certificate_templates ct ON gc.template_id = ct.id\nJOIN users u ON gc.user_id = u.user_id\nLEFT JOIN children c ON gc.child_id = c.child_id\nJOIN events e ON gc.event_id = e.id\nJOIN venues v ON e.venue_id = v.id\nJOIN cities city ON v.city_id = city.id\nWHERE gc.id = {{ $json.certificate_id }} AND gc.status != 'failed';", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2960, 1720], "id": "2dc59323-ebad-499d-94e5-c3b53255aed6", "name": "certificate-data", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "// Get input data\nconst inputData = $input.first().json;\n\n// Handle array structure\nlet certificate;\nif (Array.isArray(inputData)) {\n  certificate = inputData[0];\n} else {\n  certificate = inputData;\n}\n\nif (!certificate) {\n  throw new Error('Certificate data is missing');\n}\n\n// Data is already parsed objects\nconst certificateData = certificate.certificate_data;\nconst fields = certificate.fields;\n\n// Generate unique filename\nconst timestamp = Date.now();\nconst filename = `certificate_${certificate.id}_${timestamp}.pdf`;\nconst pdfPath = `/certificates/${filename}`;\nconst fullPath = `public${pdfPath}`;\n\n// Handle background image\nlet backgroundStyle = '';\nif (certificate.background_image) {\n  if (certificate.background_image.startsWith('data:')) {\n    backgroundStyle = `background-image: url('${certificate.background_image}');`;\n  } else {\n    // For file path, we'll use the actual file URL\n    backgroundStyle = `background-image: url('https://nibog.in${certificate.background_image}');`;\n  }\n} else {\n  backgroundStyle = `background: #f8f9fa;`;\n}\n\n// Generate HTML with ONLY dynamic fields\nconst html = `\n<!DOCTYPE html>\n<html>\n<head>\n  <style>\n    @page { \n      size: ${certificate.paper_size || 'A4'} ${certificate.orientation || 'landscape'}; \n      margin: 0; \n    }\n    body { \n      margin: 0; \n      font-family: Arial; \n      width: 100%;\n      height: 100vh;\n    }\n    .certificate-container {\n      width: 100%;\n      height: 100vh;\n      ${backgroundStyle}\n      background-size: cover;\n      background-position: center;\n      background-repeat: no-repeat;\n      position: relative;\n    }\n    .field { \n      position: absolute; \n      text-align: center; \n    }\n  </style>\n</head>\n<body>\n  <div class=\"certificate-container\">\n    ${fields.map(field => {\n      // Map field names to actual data\n      let value = '';\n      const fieldKey = field.name.toLowerCase().replace(/\\s+/g, '_');\n      \n      switch (fieldKey) {\n        case 'participant_name':\n          value = certificate.child_name || certificateData.participant_name || '';\n          break;\n        case 'event_name':\n          value = certificate.event_name || certificateData.event_name || '';\n          break;\n        case 'date':\n        case 'event_date':\n          value = new Date(certificate.event_date).toLocaleDateString() || '';\n          break;\n        case 'venue_name':\n          value = certificate.venue_name || certificateData.venue_name || '';\n          break;\n        case 'city_name':\n          value = certificate.city_name || certificateData.city_name || '';\n          break;\n        case 'certificate_number':\n          value = certificateData.certificate_number || '';\n          break;\n        default:\n          value = certificateData[fieldKey] || certificateData[field.name] || '';\n      }\n      \n      return `\n        <div class=\"field\" style=\"\n          left: ${field.x}%; \n          top: ${field.y}%; \n          font-size: ${field.font_size || 24}px;\n          color: ${field.color || '#000000'};\n          font-family: '${field.font_family || 'Arial'}', sans-serif;\n          font-weight: ${field.name.toLowerCase().includes('name') ? 'bold' : 'normal'};\n        \">\n          ${value}\n        </div>\n      `;\n    }).join('')}\n  </div>\n</body>\n</html>\n`;\n\nreturn {\n  json: {\n    html: html,\n    certificate_id: certificate.id,\n    filename: filename,\n    pdf_path: pdfPath,\n    full_path: fullPath\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2800, 1720], "id": "e4ca83dd-e2ef-41c7-a3ea-81ef77c72d0a", "name": "Generate HTML PDF"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/bookingsevents/update-status", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [400, 1580], "id": "7c9311b1-4581-4b18-87a2-ed207103eb3f", "name": "Update-status", "webhookId": "7861f993-4932-4546-947d-5eac48f4b5b3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE bookings\nSET status = '{{ $json.body.status }}',\n    updated_at = CURRENT_TIMESTAMP\nWHERE booking_id = {{ $json.body.booking_id }}\nRETURNING *;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [600, 1580], "id": "fa26848b-6c19-415b-8ede-499d5da17fb7", "name": "Bookingstatus-update", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "parents", "mode": "list", "cachedResultName": "parents"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "user_id": "={{ $json.body.user_id }}", "parent_name": "={{ $json.body.parent.parent_name }}", "email": "={{ $json.body.parent.email }}", "additional_phone": "={{ $json.body.parent.additional_phone }}"}, "matchingColumns": [], "schema": [{"id": "parent_id", "displayName": "parent_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "parent_name", "displayName": "parent_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "additional_phone", "displayName": "additional_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [540, 1240], "id": "8f6ff481-e950-47d0-819e-73f4b7d6783b", "name": "Insert Parent", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "children", "mode": "list", "cachedResultName": "children"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "parent_id": "={{ $json.parent_id }}", "full_name": "={{ $('bookings register').item.json.body.child.full_name }}", "date_of_birth": "={{ $('bookings register').item.json.body.child.date_of_birth }}", "school_name": "={{ $('bookings register').item.json.body.child.school_name }}", "gender": "={{ $('bookings register').item.json.body.child.gender }}"}, "matchingColumns": [], "schema": [{"id": "child_id", "displayName": "child_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "parent_id", "displayName": "parent_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "full_name", "displayName": "full_name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "date_of_birth", "displayName": "date_of_birth", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "school_name", "displayName": "school_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "gender", "displayName": "gender", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [700, 1240], "id": "d50b8e1b-d1aa-4eeb-b2ad-e29c4b0be825", "name": "Insert Child", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "bookings", "mode": "list", "cachedResultName": "bookings"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "user_id": "={{ $('bookings register').item.json.body.user_id }}", "event_id": "={{ $('bookings register').item.json.body.booking.event_id }}", "total_amount": "={{ $('bookings register').item.json.body.booking.total_amount }}", "payment_method": "={{ $('bookings register').item.json.body.booking.payment_method }}", "payment_status": "={{ $('bookings register').item.json.body.booking.payment_status }}", "parent_id": "={{ $('Insert Parent').item.json.parent_id }}", "terms_accepted": "={{ $('bookings register').item.json.body.booking.terms_accepted }}", "booking_ref": "={{ $('bookings register').item.json.body.booking.booking_ref }}", "status": "={{ $('bookings register').item.json.body.booking.status }}"}, "matchingColumns": [], "schema": [{"id": "booking_id", "displayName": "booking_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "booking_ref", "displayName": "booking_ref", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "parent_id", "displayName": "parent_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "event_id", "displayName": "event_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "total_amount", "displayName": "total_amount", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "payment_method", "displayName": "payment_method", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "payment_status", "displayName": "payment_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "terms_accepted", "displayName": "terms_accepted", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "cancelled_at", "displayName": "cancelled_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "completed_at", "displayName": "completed_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [880, 1240], "id": "ccc523a0-4092-4760-9f32-341a36e804b2", "name": "Insert booking", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const bookingId = $input.first().json.booking_id;\nconst childId = $('Insert Child').first().json.child_id;\nconst games = $('bookings register').first().json.body.booking_games;\n\nconst rows = games.map(game => {\n  return {\n    json: {\n      booking_id: bookingId,\n      child_id: childId,\n      slot_id: game.slot_id,\n      game_id: game.game_id,\n      game_price: game.game_price\n    }\n  };\n});\n\nreturn rows;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 1240], "id": "fb86e5dd-1093-4418-a933-ff27dcd6a615", "name": "Return games"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/bookingsevents/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [2000, 1560], "id": "0b5313c6-24df-48a5-842b-99d719cf0e83", "name": "Get single booking", "webhookId": "fad678c8-9522-4394-b4ba-8419d154ae57"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/get-by-event", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [960, 2300], "id": "e877128d-8255-4829-8557-d0dc99db7a66", "name": "Get by Event", "webhookId": "2e2cfe97-47a6-4d51-985b-64450653741a"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT pc.*\nFROM promo_codes pc\nJOIN promo_code_mappings pcm ON pcm.promocodetable_id = pc.id\nWHERE pcm.event_id = {{ $json.body.event_id }}\n  AND pc.is_active = true\n  AND pcm.is_active = true\n  AND pc.valid_from <= NOW()\n  AND pc.valid_to >= NOW()\n  AND pc.usage_count < pc.usage_limit;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1140, 2300], "id": "4447022a-f0ec-4dc5-b835-f98697ee5f24", "name": "promocode by event", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/get-by-event-games", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [600, 2300], "id": "52cab092-ecc3-4076-bd3d-53452ff300bb", "name": "get-by-event-games", "webhookId": "185dc3df-7413-4b3b-88c2-8dcbe61a9cd5"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH matched_promos AS (\n  SELECT\n    pc.*,\n    pcm.scope,\n    pcm.event_id AS mapping_event_id,\n    pcm.game_id AS mapping_game_id\n  FROM promo_codes pc\n  LEFT JOIN promo_code_mappings pcm ON pc.id = pcm.promocodetable_id\n  WHERE pc.is_active = TRUE\n    AND pc.valid_from <= now()\n    AND pc.valid_to >= now()\n    AND (\n    pcm.scope IS NULL\n    OR pcm.scope = 'all'\n    OR (pcm.scope = 'events' AND pcm.event_id = {{ $json.body.event_id }})\n    OR (\n      pcm.scope = 'games'\n      AND pcm.event_id = {{ $json.body.event_id }}\n      AND pcm.game_id = ANY(array[{{ $json.body.game_ids.join(',') }}]::int[])\n    )\n  )\n\n)\nSELECT\n  pc.id,\n  MIN(pc.promo_code) AS promo_code,\n  MIN(pc.type) AS type,\n  MIN(pc.value) AS value,\n  MIN(pc.valid_from)::date AS valid_from,\n  MIN(pc.valid_to)::date AS valid_to,\n  MIN(pc.usage_limit) AS usage_limit,\n  MIN(pc.usage_count) AS usage_count,\n  MIN(pc.minimum_purchase_amount) AS minimum_purchase_amount,\n  MIN(pc.maximum_discount_amount) AS maximum_discount_amount,\n  MIN(pc.description) AS description,\n  COALESCE(pcm.scope, 'all') AS applicable_scope,\n  array_agg(DISTINCT pcm.event_id) FILTER (WHERE pcm.scope IN ('event', 'games')) AS applicable_events,\n  array_agg(DISTINCT pcm.game_id) FILTER (WHERE pcm.scope = 'games') AS applicable_games\nFROM matched_promos pc\nLEFT JOIN promo_code_mappings pcm ON pc.id = pcm.promocodetable_id\nGROUP BY pc.id, applicable_scope;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [780, 2300], "id": "ed8afe5a-afc4-4a63-a9ed-62a6e18f9b1b", "name": "Return-available-scopes", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/validate", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [660, 2500], "id": "40feefef-b49c-423d-8a3d-0517953cf51c", "name": "Validate", "webhookId": "f56856aa-4e1c-4979-bd2f-1bcd45b76e20"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH input_data AS (\n  SELECT\n    '{{ $json.body.promo_code }}'::VARCHAR AS promo_code,\n    {{ $json.body.event_id }}::INT AS event_id,\n    array[{{ $json.body.game_ids.join(',') }}]::INT[] AS game_ids,\n    {{ $json.body.total_amount }}::NUMERIC AS total_amount\n),\nmatched_promo AS (\n  SELECT\n    pc.*,\n    pcm.scope,\n    pcm.event_id AS mapped_event_id,\n    pcm.game_id AS mapped_game_id\n  FROM promo_codes pc\n  LEFT JOIN promo_code_mappings pcm ON pc.id = pcm.promocodetable_id\n  JOIN input_data i ON LOWER(pc.promo_code) = LOWER(i.promo_code)\n  WHERE pc.is_active = true\n    AND pc.valid_from <= now()\n    AND pc.valid_to >= now()\n    AND pc.usage_count < pc.usage_limit\n    AND (\n      pcm.scope IS NULL -- 'all'\n      OR (pcm.scope = 'event' AND pcm.event_id = i.event_id)\n      OR (pcm.scope = 'games' AND pcm.event_id = i.event_id AND pcm.game_id = ANY(i.game_ids))\n    )\n  LIMIT 1\n)\nSELECT\n  CASE\n    WHEN mp.id IS NULL THEN false\n    WHEN i.total_amount < mp.minimum_purchase_amount THEN false\n    ELSE true\n  END AS is_valid,\n\n  LEAST(\n    CASE\n      WHEN mp.type = 'flat' THEN mp.value\n      WHEN mp.type = 'percentage' THEN ROUND((i.total_amount * mp.value) / 100.0, 2)\n      ELSE 0\n    END,\n    COALESCE(mp.maximum_discount_amount, 999999)\n  ) AS discount_amount,\n\n  (i.total_amount - \n    LEAST(\n      CASE\n        WHEN mp.type = 'flat' THEN mp.value\n        WHEN mp.type = 'percentage' THEN ROUND((i.total_amount * mp.value) / 100.0, 2)\n        ELSE 0\n      END,\n      COALESCE(mp.maximum_discount_amount, 999999)\n    )\n  ) AS final_amount,\n\n  CASE\n    WHEN mp.id IS NULL THEN 'Invalid or expired promo code'\n    WHEN i.total_amount < mp.minimum_purchase_amount THEN 'Minimum purchase amount not met'\n    ELSE 'Promo code applied successfully'\n  END AS message,\n\n  mp.id AS promo_id,\n  mp.promo_code,\n  mp.type,\n  mp.value,\n  mp.maximum_discount_amount\n\nFROM input_data i\nLEFT JOIN matched_promo mp ON TRUE;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [840, 2500], "id": "57f7be00-bb61-4e70-bb6c-8cff55423ecb", "name": "Promocode Validate", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const row = $('Promocode Validate').first().json;\n\nreturn [\n  {\n    json: {\n      is_valid: row.is_valid,\n      discount_amount: row.discount_amount,\n      final_amount: row.final_amount,\n      message: row.message,\n      promo_details: {\n        id: row.promo_id,\n        promo_code: row.promo_code,\n        type: row.type,\n        value: row.value,\n        maximum_discount_amount: row.maximum_discount_amount\n      }\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 2500], "id": "0ee1f6be-bc12-4c66-a283-d9cf73e4941f", "name": "Return message"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE promo_codes\nSET usage_count = usage_count + 1, updated_at = CURRENT_TIMESTAMP\nWHERE id = {{ $json.promo_id }}\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1020, 2500], "id": "d3e43916-ba81-49de-9061-8ebcb2d6b3ab", "name": "Update usage count", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/preview-validation", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [920, 1940], "id": "50d98810-c8fe-49c7-ba20-7d649a4e2c5d", "name": "Preview-Validation", "webhookId": "24ebed6f-c3a2-4fd0-9c2e-6de8691823e0"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH input_data AS (\n  SELECT\n    '{{ $json.body.promocode }}'::VARCHAR AS promo_code,\n    {{ $json.body.eventId }}::INT AS event_id,\n    array[{{ $json.body.gameIds.join(',') }}]::INT[] AS game_ids,\n    {{ $json.body.subtotal }}::NUMERIC AS subtotal\n),\npromo_base AS (\n  SELECT pc.*\n  FROM promo_codes pc\n  JOIN input_data i ON LOWER(pc.promo_code) = LOWER(i.promo_code)\n  WHERE pc.is_active = TRUE\n    AND pc.valid_from <= now()\n    AND pc.valid_to >= now()\n    AND pc.usage_count < COALESCE(pc.usage_limit, 999999)\n    AND (pc.minimum_purchase_amount IS NULL OR i.subtotal >= pc.minimum_purchase_amount)\n),\npromo_match_scope AS (\n  SELECT\n    pb.*,\n    pcm.scope,\n    pcm.event_id AS mapped_event_id,\n    pcm.game_id AS mapped_game_id,\n    i.event_id,\n    i.game_ids,\n    i.subtotal,\n    CASE\n      WHEN pcm.scope = 'games' AND pcm.event_id = i.event_id AND pcm.game_id = ANY(i.game_ids) THEN 1\n      WHEN pcm.scope = 'events' AND pcm.event_id = i.event_id THEN 2\n      WHEN pcm.scope IS NULL OR pcm.scope = 'all' THEN 3\n      ELSE 100\n    END AS match_priority\n  FROM promo_base pb\n  LEFT JOIN promo_code_mappings pcm ON pb.id = pcm.promocodetable_id\n  CROSS JOIN input_data i\n)\nSELECT *\nFROM promo_match_scope\nWHERE match_priority < 100\nORDER BY match_priority\nLIMIT 1;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1120, 1940], "id": "9d3ce39d-98cf-4af7-bb35-d1dc54a1046d", "name": "Promocode Preview", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const row = $input.first()?.json;\n\nif (!row || Object.keys(row).length === 0 || !row.promo_code) {\n  return [{\n    json: {\n      is_valid: false,\n      discount_amount: 0,\n      final_amount: row?.subtotal ?? 0,\n      message: \"Invalid or inapplicable promo code.\",\n      promo_details: {}\n    }\n  }];\n}\n\nconst subtotal = parseFloat(row.subtotal || 0);\nconst value = parseFloat(row.value || 0);\nlet discount = 0;\n\n// Handle discount calculation\nif (row.type === \"percentage\") {\n  discount = subtotal * (value / 100);\n\n  // ✅ Apply max discount only if it's not null\n  if (row.maximum_discount_amount !== null) {\n    const maxDiscount = parseFloat(row.maximum_discount_amount);\n    if (!isNaN(maxDiscount) && discount > maxDiscount) {\n      discount = maxDiscount;\n    }\n  }\n} else if (row.type === \"flat\") {\n  discount = value;\n}\n\nconst finalAmount = subtotal - discount;\n\nreturn [{\n  json: {\n    is_valid: true,\n    discount_amount: discount,\n    final_amount: finalAmount,\n    message: \"Promo code applied successfully.\",\n    promo_details: row\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1300, 1940], "id": "5cc3e3a2-6e6c-40bf-8e20-cf92d04b67a7", "name": "Return Preview"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/promocode/rollback-usage", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1320, 2380], "id": "605c2d99-380d-4f4b-98ce-0c224d62cfb8", "name": "Rollback-A<PERSON>", "webhookId": "2ee0d57f-cb4b-4adb-a665-8b78229e3c41"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE promo_codes\nSET usage_count = GREATEST(usage_count - 1, 0),\n    updated_at = CURRENT_TIMESTAMP\nWHERE id = {{ $json.body.id }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1520, 2380], "id": "ed767bae-3ced-4e1a-8db7-c6f5ce4632fe", "name": "Reduce-usage count", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Get the correct user_id from the parents table\nSELECT p.user_id, p.parent_name,p.email\nFROM parents p \nWHERE p.parent_id = {{ $json.parent_id }};", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2920, 1540], "id": "c283589a-767a-440b-aca9-4dbd21004f3f", "name": "Validate user", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/certificates/get-all", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2620, 980], "id": "c01d0741-ec88-4a35-a3ee-82502f7cd447", "name": "Get-all-certificates", "webhookId": "e1be1e2a-9282-428a-bad2-59c6c42436c2"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  gc.*,\n  bg.game_name,\n  c.full_name AS child_name\nFROM \n  generated_certificates gc\nLEFT JOIN \n  baby_games bg ON gc.game_id = bg.id\nLEFT JOIN \n  children c ON gc.child_id = c.child_id\nORDER BY \n  gc.generated_at DESC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2400, 980], "id": "1cfe1008-5e69-4e93-a2ef-b9f67d79b64a", "name": "return certificates", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/certificates/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2580, 1720], "id": "e7dccc1b-5673-48b9-b7af-ed123cec8d2e", "name": "Get-single Certificate", "webhookId": "fad2885c-9f6f-4d14-bbe1-58c3510128c3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  gc.*,\n  bg.game_name,\n  c.full_name AS child_name\nFROM \n  generated_certificates gc\nLEFT JOIN \n  baby_games bg ON gc.game_id = bg.id\nLEFT JOIN \n  children c ON gc.child_id = c.child_id\nWHERE \n  gc.id = {{ $json.body.id }}\nORDER BY \n  gc.generated_at DESC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2360, 1720], "id": "8dde4291-24f7-468f-9bcc-6a026f4bc3cf", "name": "Return single certificate", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/validateqrcode", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1880, 2500], "id": "ca12ee6b-d466-40b3-ba86-2782c200e942", "name": "QRcode validate", "webhookId": "5316151d-6a0c-40a6-96a4-43746f2d5ac8"}, {"parameters": {"content": "## QR code Validation\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 820, "width": 1040}, "type": "n8n-nodes-base.stickyNote", "position": [1820, 2360], "typeVersion": 1, "id": "2ff6925c-c088-404c-b504-59a8b4cc549b", "name": "Sticky Note19"}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "booking_games", "mode": "list", "cachedResultName": "booking_games"}, "returnAll": true, "where": {"values": [{"column": "booking_id", "value": "={{ $json.body.booking_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [2100, 2500], "id": "b4244cb3-93d0-4ea8-993f-9594739e12bf", "name": "bookingdataattendance check", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const data = items.map(item => item.json);\n\nif (data.length === 0) {\n  return [\n    {\n      json: {\n        status: \"invalid QR code\",\n        data: [],\n      }\n    }\n  ];\n}\n\nconst allAttended = data.every(entry => entry.attendance_status === \"Attended\");\nconst anyRegistered = data.some(entry => entry.attendance_status === \"Registered\");\n\nif (allAttended) {\n  return [\n    {\n      json: {\n        status: \"already used\",\n        data: data,\n      }\n    }\n  ];\n}\n\nif (anyRegistered) {\n  return [\n    {\n      json: {\n        status: \"valid QR code\",\n        data: data,\n      }\n    }\n  ];\n}\n\n// Fallback if nothing matches\nreturn [\n  {\n    json: {\n      status: \"invalid QR code\",\n      data: [],\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2260, 2500], "id": "f9d52356-7d13-42ba-aa11-23a994485ddd", "name": "Code6"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.status }}", "rightValue": "already used", "operator": {"type": "string", "operation": "equals"}, "id": "2384231e-8625-4a6d-9609-0dd5a5ec6839"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0585e298-87ad-4d17-b2b7-905b3a1ce3ec", "leftValue": "={{ $json.status }}", "rightValue": "valid QR code", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1da7fa61-c999-499d-972b-a95068d1c5e4", "leftValue": "={{ $json.status }}", "rightValue": "invalid QR code", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [2440, 2500], "id": "a0bdee12-9a2f-4e79-9896-51bdb42daebe", "name": "Switch2"}, {"parameters": {"jsCode": "return items.map(item => item.json);\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1940, 2760], "id": "14c79217-b969-4125-a5cc-cfebb60e79a5", "name": "Code7"}, {"parameters": {"jsCode": "return items.map(item => item.json);\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2200, 2800], "id": "baba6d92-1c36-464b-85ff-110b718d9ae2", "name": "Code8"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nreturn items.map(item => item.json);\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2680, 2420], "id": "ef1ab13d-c0be-4978-9143-cb9c77be61ef", "name": "Code9"}, {"parameters": {"operation": "update", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "booking_games", "mode": "list", "cachedResultName": "booking_games"}, "columns": {"mappingMode": "defineBelow", "value": {"is_active": true, "booking_game_id": "={{ $json.data[0].booking_game_id }}", "attendance_status": "Attended"}, "matchingColumns": ["booking_game_id"], "schema": [{"id": "booking_game_id", "displayName": "booking_game_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "booking_id", "displayName": "booking_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "child_id", "displayName": "child_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "game_id", "displayName": "game_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "game_price", "displayName": "game_price", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "attendance_status", "displayName": "attendance_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_active", "displayName": "is_active", "required": false, "defaultMatch": false, "display": true, "type": "boolean", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [2400, 2800], "id": "1742d96f-2cd5-470d-b019-48d94f940606", "name": "Postgres7", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const data = items.map(item => item.json);\n\nlet status = \"valid QR code\";\n\nreturn [\n  {\n    json: {\n      status: status,\n      data: data\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2580, 2800], "id": "aed2c5c6-1183-4380-bea2-19c163733d15", "name": "Code10"}, {"parameters": {"path": "v1/nibog/events/upcoming-events", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1540, 2200], "id": "f4daca27-d3ca-4bd4-ba16-da00d347dad1", "name": "Upcoming-events", "webhookId": "af1b574b-3fb3-4029-8554-48d843c6fbef"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT *\nFROM events\nWHERE event_date >= CURRENT_DATE\n  AND status = 'Published'\nORDER BY event_date ASC;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1320, 2200], "id": "6eed01c3-111b-452a-b783-2b5bab5f282e", "name": "Upcoming-events1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "Event Registration apis\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 340, "width": 1080}, "type": "n8n-nodes-base.stickyNote", "position": [-3320, 2320], "typeVersion": 1, "id": "257eeea4-f5a8-4aa2-bc87-ad8f0c2d0578", "name": "Sticky Note20"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/events/upcoming-events-by-cityid", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3260, 2440], "id": "311836f7-fe8d-4a4c-834e-389b5120efa0", "name": "Upcomingevents-bycity", "webhookId": "8fa58a95-6563-4070-bf05-0767d6565809"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  id,\n  title,\n  description,\n  city_id,\n  venue_id,\n  to_char(event_date, 'YYYY-MM-DD') AS event_date,\n  status,\n  created_at,\n  updated_at\nFROM events\nWHERE event_date >= CURRENT_DATE\n  AND status = 'Published'\n  AND city_id = {{ $json.body.city_id }}\nORDER BY event_date ASC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-3060, 2440], "id": "8a094b2a-b5c9-4c40-b891-601aa6c816a7", "name": "get-upcoming-events", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/events/get-games-by-ageandevent", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2780, 2440], "id": "54211c94-6299-457f-9a05-c527ac7b584f", "name": "Get-games-basedon-age", "webhookId": "de81c1b0-3601-4434-9f5d-505c59289ab9"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  egws.id AS slot_id,\n  egws.event_id,\n  egws.game_id,\n  COALESCE(egws.custom_title, bg.game_name) AS title,\n  COALESCE(egws.custom_description, bg.description) AS description,\n  COALESCE(egws.custom_price, bg.price) AS listed_price,\n  egws.start_time,\n  egws.end_time,\n  egws.slot_price,\n  egws.max_participants,\n  bg.min_age,\n  bg.max_age,\n  bg.duration_minutes,\n  TO_CHAR((e.event_date AT TIME ZONE 'UTC')::date, 'YYYY-MM-DD') AS event_date,  -- Force plain date string\n  v.venue_name\nFROM public.event_games_with_slots AS egws\nJOIN public.baby_games AS bg\n  ON egws.game_id = bg.id\nJOIN public.events AS e\n  ON egws.event_id = e.id\nJOIN public.venues AS v\n  ON e.venue_id = v.id\nWHERE\n  egws.event_id = {{ $json.body.event_id }}\n  AND {{ $json.body.child_age }} BETWEEN bg.min_age AND bg.max_age\n  AND bg.is_active = TRUE\nORDER BY egws.start_time;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2560, 2440], "id": "82a9d88d-d747-474b-b0f1-01a74b9dfdab", "name": "gamesbyage", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "Pending Bookings\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 620, "width": 560, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-20, 3520], "typeVersion": 1, "id": "10e0b65e-a3c2-4306-8315-fef96b56ee94", "name": "Sticky Note21"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/pending-bookings/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [60, 3640], "id": "bb7b3138-fd6e-4299-8fbf-76443bae4909", "name": "Pending-bookings create", "webhookId": "21631d00-971e-4be8-88f4-1d01213a8605"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "pending_bookings", "mode": "list", "cachedResultName": "pending_bookings"}, "columns": {"mappingMode": "defineBelow", "value": {"transaction_id": "={{ $json.body.transaction_id }}", "user_id": "={{ $json.body.user_id }}", "booking_data": "={{ $json.body.booking_data }}", "status": "={{ $json.body.status }}", "expires_at": "={{ $json.body.expires_at }}"}, "matchingColumns": [], "schema": [{"id": "pending_booking_id", "displayName": "pending_booking_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "transaction_id", "displayName": "transaction_id", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "user_id", "displayName": "user_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "booking_data", "displayName": "booking_data", "required": true, "defaultMatch": false, "display": true, "type": "object", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "expires_at", "displayName": "expires_at", "required": true, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [280, 3640], "id": "d43852b1-5866-4663-b9e8-bd5ad546f694", "name": "Insert pending", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/pending-bookings/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [60, 3820], "id": "d6eec999-446e-4dc8-bae7-11653d2a1ac5", "name": "Get-pending data", "webhookId": "2ed44b1e-f739-46ae-9e51-48b34a8e7a67"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * \nFROM pending_bookings\nWHERE transaction_id = '{{ $json.body.transaction_id }}';", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [280, 3820], "id": "5270ef9c-62fb-447f-9e4b-7e023f8f0cab", "name": "Get-data", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/pending-bookings/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [60, 3980], "id": "a9ea0aae-eccc-46e8-9948-1826c9b1362f", "name": "Delete-pendingdata", "webhookId": "f55b81a2-0268-434e-ba52-e47958f1c903"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DELETE FROM pending_bookings\nWHERE transaction_id = '{{ $json.body.transaction_id }}';", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [280, 3980], "id": "07dd37bf-57f9-44b1-a503-0cd2b9eac4bb", "name": "Delete pending", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const bookingId = $('Insert booking').first().json.booking_id;\nconst addons = $('bookings register').first().json.body.booking_addons || [];\n\nconst rows = [];\n\nif (Array.isArray(addons) && addons.length > 0) {\n  for (const addon of addons) {\n    if (addon.variants && Array.isArray(addon.variants)) {\n      for (const variant of addon.variants) {\n        rows.push({\n          json: {\n            booking_id: bookingId,\n            addon_id: addon.addon_id,\n            variant_id: variant.variant_id,\n            quantity: variant.quantity\n          }\n        });\n      }\n    } else {\n      rows.push({\n        json: {\n          booking_id: bookingId,\n          addon_id: addon.addon_id,\n          variant_id: null,\n          quantity: addon.quantity\n        }\n      });\n    }\n  }\n}\n\nreturn rows;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2140, 1120], "id": "cc110399-eb5e-400a-9d77-11d69a6aa155", "name": "Insert addons"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "booking_addons", "mode": "list", "cachedResultName": "booking_addons"}, "columns": {"mappingMode": "defineBelow", "value": {"booking_id": "={{ $json.booking_id }}", "addon_id": "={{ $json.addon_id }}", "variant_id": "={{ $json.variant_id }}", "quantity": "={{ $json.quantity }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "booking_id", "displayName": "booking_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "addon_id", "displayName": "addon_id", "required": true, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "variant_id", "displayName": "variant_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "quantity", "displayName": "quantity", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [2300, 1120], "id": "02ae2e2a-3ce5-41b1-8160-d397ec51ceb1", "name": "Addons inserted", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const promoCode = $('bookings register').first().json.body.promo_code;\n\nif (!promoCode) {\n  return []; // No promo code, skip\n}\n\nreturn [\n  {\n    json: {\n      query: `\n        UPDATE promo_codes\n        SET usage_count = usage_count + 1\n        WHERE promo_code = '${promoCode}'\n      `\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [920, 1460], "id": "97214b79-6b30-4673-9a52-ec98f5b6b8a0", "name": "Promocode usage update"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "{{$json[\"query\"]}}", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1120, 1460], "id": "6fb67ae3-61ce-4b53-8436-ea58e94d43b0", "name": "Promcode updae", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "return [\n  {\n    json: {\n      success: true,\n      message: \"Booking created successfully\",\n      booking_id: $('Insert booking').first().json.booking_id\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1300, 1460], "id": "e9f314d2-f317-40fc-b934-0b34d7d1a8c6", "name": "webhook response"}, {"parameters": {"jsCode": "const bookingId = $input.first().json.booking_id;\nconst eventId = $('Insert booking').first().json.event_id;\nconst games = $('bookings register').first().json.body.booking_games;\n\nconst updates = games.map(game => {\n  return {\n    json: {\n      query: `\n        UPDATE event_games_with_slots\n        SET max_participants = max_participants - 1\n        WHERE event_id = ${eventId} AND game_id = ${game.game_id};\n      `\n    }\n  };\n});\n\nreturn updates;\n\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1420, 1240], "id": "821e00b6-6bee-43c4-9ad7-4b94bbae248b", "name": "Update max participants"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "{{$json[\"query\"]}}", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1600, 1240], "id": "ccb14e52-ca09-4dd1-8361-fe01162060b0", "name": "execute update max", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const body = $('bookings register').first().json.body;\n\nreturn [\n  {\n    json: {\n      hasAddons: (body.booking_addons || []).length > 0,\n      hasPromocode: !!body.promo_code\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 1240], "id": "0a466a86-9b35-468b-827e-47ffebc47880", "name": "has Addons"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"id": "24b55646-851a-45cf-9cc4-d3c6bef1bc68", "leftValue": "={{$json[\"hasAddons\"]}}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "looseTypeValidation": true, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1940, 1240], "id": "125f092c-1dd7-479a-99aa-588f8a9972b3", "name": "If2"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "{{$json[\"query\"]}}", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1760, 1520], "id": "d5e78861-4ad9-4df1-8258-d7d9b367f9c3", "name": "Promcode updae1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const promoCode = $('bookings register').first().json.body.promo_code;\n\nif (!promoCode) {\n  return []; // No promo code, skip\n}\n\nreturn [\n  {\n    json: {\n      query: `\n        UPDATE promo_codes\n        SET usage_count = usage_count + 1\n        WHERE promo_code = '${promoCode}'\n      `\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 1520], "id": "eb863561-75ab-4cb9-b4ab-97a22aad5894", "name": "Promocode usage update1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "45205a96-13f5-4797-8a4f-b720e64c07ba", "leftValue": "={{ $json[\"hasPromcode\"] }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2440, 1240], "id": "752a279a-64ec-4863-a3e5-4971d9a27c54", "name": "If3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n    b.booking_id,\n    b.booking_ref,\n    b.user_id,\n    b.event_id,\n    b.status,\n    b.total_amount,\n    b.payment_method,\n    b.payment_status,\n    b.terms_accepted,\n    b.is_active,\n    b.created_at AS booking_created_at,\n    b.updated_at AS booking_updated_at,\n    b.cancelled_at,\n    b.completed_at,\n\n    p.parent_id,\n    p.parent_name,\n    p.email AS parent_email,\n    p.additional_phone,\n    p.created_at AS parent_created_at,\n    p.updated_at AS parent_updated_at,\n\n    c.child_id,\n    c.full_name AS child_name,\n    c.date_of_birth,\n    c.gender,\n    c.school_name,\n    c.created_at AS child_created_at,\n    c.updated_at AS child_updated_at\n\nFROM bookings b\nJOIN parents p ON b.parent_id = p.parent_id\nLEFT JOIN children c ON p.parent_id = c.parent_id\nWHERE b.booking_id = {{ $json.body.booking_id }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [2200, 1560], "id": "544d8a0e-a035-48b9-a048-6a1c8d3a49b0", "name": "Get booking-by-id", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n    -- Booking Info\n    b.booking_id,\n    b.booking_ref,\n    b.status AS booking_status,\n    b.total_amount,\n    b.payment_method,\n    b.payment_status,\n    b.terms_accepted,\n    b.is_active AS booking_active,\n    b.created_at AS booking_created_at,\n    b.updated_at AS booking_updated_at,\n    b.cancelled_at,\n    b.completed_at,\n\n    -- Parent Info\n    p.parent_id,\n    p.parent_name,\n    p.email AS parent_email,\n    p.additional_phone,\n    p.is_active AS parent_active,\n\n    -- User Info\n    u.user_id,\n    u.full_name AS user_full_name,\n    u.email AS user_email,\n    u.phone,\n    u.email_verified,\n    u.phone_verified,\n    u.city_id AS user_city_id,\n    u.accepted_terms,\n    u.terms_accepted_at,\n    u.is_active AS user_active,\n    u.is_locked,\n    u.locked_until,\n    u.deactivated_at,\n    u.created_at AS user_created_at,\n    u.updated_at AS user_updated_at,\n    u.last_login_at,\n\n    -- Child Info\n    c.child_id,\n    c.full_name AS child_name,\n    c.date_of_birth,\n    c.gender,\n    c.school_name,\n    c.is_active AS child_active,\n\n    -- Event Info\n    e.id AS event_id,\n    e.title AS event_title,\n    e.description AS event_description,\n    e.event_date,\n    e.status AS event_status,\n    e.created_at AS event_created_at,\n\n    -- Venue Info\n    v.id AS venue_id,\n    v.venue_name,\n    v.address AS venue_address,\n    v.capacity AS venue_capacity,\n    v.is_active AS venue_active,\n    v.created_at AS venue_created_at,\n    v.updated_at AS venue_updated_at,\n\n    -- Event City Info\n    ci.id AS event_city_id,\n    ci.city_name,\n    ci.state,\n\n    -- Slot Info\n    s.id AS slot_id,\n    s.custom_title AS slot_title,\n    s.custom_description AS slot_description,\n    s.custom_price,\n    s.start_time,\n    s.end_time,\n    s.slot_price,\n    s.max_participants,\n    s.created_at AS slot_created_at,\n    s.updated_at AS slot_updated_at,\n\n    -- Booking Game Info\n    bgm.booking_game_id,\n    bgm.game_price,\n    bgm.attendance_status,\n    bgm.created_at AS game_booking_created_at\n\nFROM bookings b\n\nJOIN parents p ON b.parent_id = p.parent_id\nJOIN users u ON p.user_id = u.user_id\nLEFT JOIN children c ON c.parent_id = p.parent_id\nJOIN events e ON b.event_id = e.id\n\n-- Venue and City joins from events\nJOIN venues v ON e.venue_id = v.id\nJOIN cities ci ON e.city_id = ci.id\n\nLEFT JOIN booking_games bgm ON bgm.booking_id = b.booking_id\nLEFT JOIN event_games_with_slots s ON s.id = bgm.slot_id\n\nWHERE b.booking_ref = '{{ $json.body.booking_ref_id }}'\n\nORDER BY \n    b.booking_id DESC,\n    c.child_id,\n    bgm.booking_game_id;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [2140, 3020], "id": "7029f45d-0ac8-4efe-adec-01ef198b883a", "name": "Postgres6", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/events/participants", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3260, 1920], "id": "8627edbf-ad1d-4870-9209-1b95438f45af", "name": "Participants", "webhookId": "517a9048-3ac1-44ae-a0aa-8122a4968bd8"}, {"parameters": {"jsCode": "const eventId = $input.first().json.body.event_id;\n\nif (!eventId || isNaN(eventId)) {\n  throw new Error('Valid Event ID is required');\n}\n\nreturn {\n  json: {\n    event_id: parseInt(eventId)\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3040, 1920], "id": "55df1427-0c2c-470a-83b9-194914e83e29", "name": "Extract id"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  e.id,\n  e.title as event_name,\n  e.event_date,\n  v.venue_name as venue_name,\n  c.city_name as city_name\nFROM events e\nJOIN venues v ON e.venue_id = v.id\nJOIN cities c ON v.city_id = c.id\nWHERE e.id = {{ $json.event_id }};", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2820, 1920], "id": "3cd889b0-383d-4c92-9d0d-99e9ecf38493", "name": "Get event details", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n    b.booking_id,\n    b.booking_ref,\n    p.parent_id,\n    p.parent_name,\n    p.email,\n    p.additional_phone,\n    c.child_id,\n    c.full_name AS child_name,\n    c.date_of_birth,\n    c.gender,\n    e.title AS event_title,\n    e.event_date,\n    v.venue_name,\n    g.id AS game_id,\n    g.game_name\nFROM bookings b\nJOIN parents p ON b.parent_id = p.parent_id\nJOIN booking_games bg ON b.booking_id = bg.booking_id\nJOIN children c ON bg.child_id = c.child_id\nJOIN events e ON b.event_id = e.id\nJOIN venues v ON e.venue_id = v.id\nJOIN baby_games g ON bg.game_id = g.id\nWHERE \n    b.status = 'Confirmed'\n    AND bg.attendance_status = 'Attended'\n    AND b.event_id = {{ $('Extract id').item.json.event_id }};", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2600, 1920], "id": "c7c1377b-409a-4e4c-a19c-d9330963eba2", "name": "Get participants", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"jsCode": "const allInputs = $input.all();\n\n// Event data is first input → first item only\nconst eventData = allInputs[0].json;\n\n// Participants are from second input (starting from index 1 onward)\nconst participants = allInputs.slice(1).map(item => item.json);\n\nif (!eventData) {\n  throw new Error('Event not found');\n}\n\nreturn [\n  {\n    json: {\n      event_id: eventData.id,\n      event_name: eventData.event_name,\n      event_date: eventData.event_date,\n      venue_name: eventData.venue_name,\n      city_name: eventData.city_name,\n      total_participants: participants.length,\n      participants: participants\n    }\n  }\n];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2380, 1920], "id": "5b27b0dc-41de-47e9-a703-06fade415a13", "name": "Format Response"}, {"parameters": {"path": "v1/nibog/complete/event", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2700, 2800], "id": "4619f79a-c8ee-4153-8edc-128bff2de133", "name": "completed events", "webhookId": "06f2faf9-dfdd-4fd3-bd9a-9e347f6e46de"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n    e.id AS event_id,\n    e.title AS event_name,\n    c.city_name,\n    v.venue_name,\n    TO_CHAR(e.event_date, 'Mon DD, YYYY') AS event_date,\n\n    -- JSON array of games\n    (\n        SELECT JSON_AGG(JSON_BUILD_OBJECT('id', g.id, 'name', g.name))\n        FROM (\n            SELECT DISTINCT bg_template.id, bg_template.game_name AS name\n            FROM bookings b2\n            JOIN booking_games bg2 ON bg2.booking_id = b2.booking_id\n            JOIN baby_games bg_template ON bg_template.id = bg2.game_id\n            WHERE b2.event_id = e.id\n        ) g\n    ) AS games,\n\n    -- Total unique bookings\n    COUNT(DISTINCT b.booking_id) AS registrations,\n\n    -- Only count booking_ids where all their booking_games have status = 'Attended'\n    COUNT(DISTINCT CASE \n        WHEN NOT EXISTS (\n            SELECT 1\n            FROM booking_games bgx\n            WHERE bgx.booking_id = b.booking_id\n              AND bgx.attendance_status <> 'Attended'\n        )\n        THEN b.booking_id\n    END) AS attendance_count,\n\n    -- Attendance % based on fully attended bookings only\n    CONCAT(\n        ROUND(\n            COUNT(DISTINCT CASE \n                WHEN NOT EXISTS (\n                    SELECT 1\n                    FROM booking_games bgx\n                    WHERE bgx.booking_id = b.booking_id\n                      AND bgx.attendance_status <> 'Attended'\n                )\n                THEN b.booking_id\n            END)::DECIMAL\n            / NULLIF(COUNT(DISTINCT b.booking_id), 0) * 100,\n        0\n        ),\n        '%'\n    ) AS attendance_percentage,\n\n    -- Revenue\n    '₹' || TO_CHAR(COALESCE(SUM(b.total_amount), 0), 'FM999,999.00') AS revenue\n\nFROM events e\nJOIN cities c ON e.city_id = c.id\nJOIN venues v ON e.venue_id = v.id\nLEFT JOIN bookings b ON b.event_id = e.id\nLEFT JOIN booking_games bg ON bg.booking_id = b.booking_id\nLEFT JOIN baby_games bg_template ON bg_template.id = bg.game_id\n\n-- ✅ Past & published events\nWHERE e.event_date < CURRENT_DATE AND e.status = 'Published'\n\nGROUP BY e.id, e.title, c.city_name, v.venue_name, e.event_date\nORDER BY e.event_date DESC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2480, 2800], "id": "4dd0a766-d2f2-4605-bb41-5990b41a6f83", "name": "completedevents", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "Completed events Api", "height": 320, "width": 540, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-2760, 2700], "typeVersion": 1, "id": "63fd6608-07e0-4a6f-914a-79106fb7f8db", "name": "Sticky Note18"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "addon_images", "mode": "list", "cachedResultName": "addon_images"}, "columns": {"mappingMode": "defineBelow", "value": {"addon_id": "={{ $json.addon_id }}", "image_url": "={{ $json.image_url }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "addon_id", "displayName": "addon_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "image_url", "displayName": "image_url", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1740, 3580], "id": "2f7fb898-dd55-4605-b428-3586dc5043d8", "name": "Postgres8", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "addon_variants", "mode": "list", "cachedResultName": "addon_variants"}, "columns": {"mappingMode": "defineBelow", "value": {"addon_id": "={{ $json.addon_id }}", "name": "={{ $json.name }}", "price_modifier": "={{ $json.price_modifier }}", "sku": "={{ $json.sku }}", "stock_quantity": "={{ $json.stock_quantity }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": false}, {"id": "addon_id", "displayName": "addon_id", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "price_modifier", "displayName": "price_modifier", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}, {"id": "sku", "displayName": "sku", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "stock_quantity", "displayName": "stock_quantity", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1620, 3800], "id": "bc85e369-5b2f-4372-b97f-d0c3c868f4f6", "name": "Postgres9", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/user/booking", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3100, 3220], "id": "ea5db245-2dde-4996-bfcc-658bf373fb72", "name": "User bookings", "webhookId": "5b7557c0-57ac-407c-ae50-adb7b108a860"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  u.user_id,\n  u.full_name,\n  u.email,\n  u.phone,\n  u.city_id,\n  u.is_active,\n  u.created_at,\n  u.updated_at,\n\n  json_agg(\n    json_build_object(\n      'booking_id', b.booking_id,\n      'booking_ref', b.booking_ref,\n      'status', b.status,\n      'total_amount', b.total_amount,\n      'payment_status', b.payment_status,\n      'created_at', b.created_at,\n\n      -- Event info\n      'event', json_build_object(\n        'event_id', e.id,\n        'title', e.title,\n        'description', e.description,\n        'event_date', e.event_date,\n        'status', e.status\n      ),\n\n      -- Games booked with slot info (only those actually booked)\n      'games', (\n        SELECT json_agg(\n          json_build_object(\n            'game_id', g.id,\n            'game_name', g.game_name,\n            'description', g.description,\n            'duration_minutes', g.duration_minutes,\n            'child_id', bg.child_id,\n            'attendance_status', bg.attendance_status,\n            'slot_info', (\n              SELECT json_build_object(\n                'slot_id', egws.id,\n                'custom_title', egws.custom_title,\n                'custom_description', egws.custom_description,\n                'custom_price', egws.custom_price,\n                'slot_price', egws.slot_price,\n                'start_time', egws.start_time,\n                'end_time', egws.end_time,\n                'max_participants', egws.max_participants\n              )\n              FROM event_games_with_slots egws\n              WHERE egws.event_id = b.event_id\n                AND egws.game_id = bg.game_id\n              LIMIT 1\n            )\n          )\n        )\n        FROM booking_games bg\n        JOIN baby_games g ON bg.game_id = g.id\n        WHERE bg.booking_id = b.booking_id\n      ),\n\n      -- Add-ons\n      'addons', (\n        SELECT json_agg(\n          json_build_object(\n            'addon_id', a.id,\n            'name', a.name,\n            'description', a.description,\n            'price', a.price,\n            'category', a.category,\n            'has_variants', a.has_variants,\n            'quantity', ba.quantity\n          )\n        )\n        FROM booking_addons ba\n        JOIN addons a ON ba.addon_id = a.id\n        WHERE ba.booking_id = b.booking_id\n      )\n    )\n  ) AS bookings\n\nFROM users u\nLEFT JOIN bookings b ON u.user_id = b.user_id\nLEFT JOIN events e ON b.event_id = e.id\nWHERE u.user_id =  {{ $json.body.user_id }} -- Replace with actual user ID\nGROUP BY u.user_id;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2880, 3220], "id": "5aa96261-1166-46d2-9941-96fe2e80d927", "name": "Postgres10", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"height": 660, "width": 680}, "type": "n8n-nodes-base.stickyNote", "position": [-3200, 3100], "typeVersion": 1, "id": "1ce85d31-f39c-45f0-b436-18c4405cd15f", "name": "Sticky Note22"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/tickect/booking_ref/details", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1920, 3020], "id": "7082fa0f-bdd7-4563-83da-920b1c753a79", "name": "Get booking_ref details", "webhookId": "eb42306d-0b38-4431-810a-ebba6c43e0de"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/event-registration-new/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3120, 3400], "id": "790fca9f-b06d-47bf-9471-38e0c153c915", "name": "event registration single get1", "webhookId": "daf0f6b9-06d0-4473-bc7c-d8e89cf25990"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH game_slots AS (\n  SELECT\n    eg.event_id,\n    eg.game_id,\n    json_agg(\n      json_build_object(\n        'custom_title', eg.custom_title,\n        'custom_description', eg.custom_description,\n        'custom_price', eg.custom_price,\n        'start_time', eg.start_time,\n        'end_time', eg.end_time,\n        'slot_price', eg.slot_price,\n        'max_participants', eg.max_participants\n      ) ORDER BY eg.start_time\n    ) AS slots\n  FROM event_games_with_slots eg\n  WHERE eg.event_id = {{ $json.body.id }}\n  GROUP BY eg.event_id, eg.game_id\n)\n\nSELECT\n  e.id AS event_id,\n  e.title AS event_title,\n  e.description AS event_description,\n  TO_CHAR(e.event_date, 'YYYY-MM-DD') AS event_date,\n  e.status AS event_status,\n  e.created_at AS event_created_at,\n  e.updated_at AS event_updated_at,\n\n  c.id AS city_id,\n  c.city_name,\n  c.state,\n  c.is_active AS city_is_active,\n  c.created_at AS city_created_at,\n  c.updated_at AS city_updated_at,\n\n  v.id AS venue_id,\n  v.venue_name,\n  v.address AS venue_address,\n  v.capacity AS venue_capacity,\n  v.is_active AS venue_is_active,\n  v.created_at AS venue_created_at,\n  v.updated_at AS venue_updated_at,\n\n  COALESCE(\n    json_agg(\n      json_build_object(\n        'game_id', bg.id,\n        'game_title', bg.game_name,\n        'game_description', bg.description,\n        'min_age', bg.min_age,\n        'max_age', bg.max_age,\n        'game_duration_minutes', bg.duration_minutes,\n        'categories', bg.categories,\n        'slots', gs.slots\n      )\n    ) FILTER (WHERE bg.id IS NOT NULL),\n    '[]'::json\n  ) AS games\n\nFROM events e\nJOIN cities c ON e.city_id = c.id\nJOIN venues v ON e.venue_id = v.id\nLEFT JOIN game_slots gs ON e.id = gs.event_id\nLEFT JOIN baby_games bg ON gs.game_id = bg.id\nWHERE e.id = {{ $json.body.id }}\nGROUP BY e.id, c.id, v.id;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2880, 3400], "id": "564afaef-6da8-4998-aec8-9da9d93edff2", "name": "Event registration single get1", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/events/get-games-by-ageandevent-new", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3120, 3600], "id": "83f1988e-3ead-46c4-80fd-d89187baa5cf", "name": "Get-games-basedon-age1", "webhookId": "de81c1b0-3601-4434-9f5d-505c59289ab9"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH game_details AS (\n  SELECT DISTINCT ON (bg.id)\n    egws.event_id,\n    bg.id AS game_id,\n    COALESCE(egws.custom_title, bg.game_name) AS title,\n    COALESCE(egws.custom_description, bg.description) AS description,\n    egws.custom_price AS listed_price,\n    bg.min_age,\n    bg.max_age,\n    bg.duration_minutes,\n    TO_CHAR((e.event_date AT TIME ZONE 'UTC')::date, 'YYYY-MM-DD') AS event_date,\n    v.venue_name\n  FROM public.event_games_with_slots egws\n  JOIN public.baby_games bg ON egws.game_id = bg.id\n  JOIN public.events e ON egws.event_id = e.id\n  JOIN public.venues v ON e.venue_id = v.id\n  WHERE\n    egws.event_id = {{ $json.body.event_id }}\n    AND {{ $json.body.child_age }} BETWEEN bg.min_age AND bg.max_age\n    AND bg.is_active = TRUE\n),\ngame_slots AS (\n  SELECT\n    egws.game_id,\n    json_agg(\n      json_build_object(\n        'slot_id', egws.id,\n        'start_time', egws.start_time,\n        'end_time', egws.end_time,\n        'slot_price', egws.slot_price,\n        'max_participants', egws.max_participants\n      ) ORDER BY egws.start_time\n    ) AS slots\n  FROM public.event_games_with_slots egws\n  WHERE egws.event_id = {{ $json.body.event_id }}\n  GROUP BY egws.game_id\n)\n\nSELECT\n  gd.*,\n  gs.slots\nFROM game_details gd\nJOIN game_slots gs ON gd.game_id = gs.game_id\nORDER BY gd.game_id;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2860, 3600], "id": "6364414b-eac2-4b51-8a63-2443ed1ef4ed", "name": "gamesbyage1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/homesection/create", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [760, 3620], "id": "3ccbd095-2519-41a5-b051-448bcabb8d23", "name": "Homepage sections created", "webhookId": "3292c6c7-d88f-4df7-a8b6-d0b23decab1b"}, {"parameters": {"path": "v1/nibog/homesection/get", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [760, 3820], "id": "84aaf0fc-41fd-4ee0-aed6-77898e5e8cc4", "name": "Home section Get", "webhookId": "099f2dfc-35f4-4d0d-a419-05b2e778f2b0"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "homepage_sections", "mode": "list", "cachedResultName": "homepage_sections"}, "columns": {"mappingMode": "defineBelow", "value": {"image_path": "={{ $json.body.image_path }}", "status": "={{ $json.body.status }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "number", "canBeUsedToMatch": true, "removed": true}, {"id": "image_path", "displayName": "image_path", "required": true, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}, {"id": "updated_at", "displayName": "updated_at", "required": false, "defaultMatch": false, "display": true, "type": "dateTime", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [920, 3620], "id": "eb6107c8-683e-4f98-aeeb-8b92f2ac1d2f", "name": "creating homesection", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "homepage_sections", "mode": "list", "cachedResultName": "homepage_sections"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [920, 3820], "id": "c1212f7f-9fd0-474f-9edf-72d99b31928f", "name": "homesection get", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/homesection/delete", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [760, 4020], "id": "353961f4-ccf9-42b8-b4a8-5b3da4d11fd1", "name": "home section del", "webhookId": "d6704092-971c-4d62-aaa6-7f59fa5cbb39"}, {"parameters": {"operation": "deleteTable", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "homepage_sections", "mode": "list", "cachedResultName": "homepage_sections"}, "deleteCommand": "delete", "where": {"values": [{"column": "id", "value": "={{ $json.body.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [920, 4020], "id": "60fa012e-4d42-4304-bcb8-972d1db1f345", "name": "home section delete", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"content": "## home page images\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 640, "width": 800}, "type": "n8n-nodes-base.stickyNote", "position": [680, 3540], "typeVersion": 1, "id": "55c67107-7ba6-46c0-bc49-2edd22b27845", "name": "Sticky Note23"}, {"parameters": {"path": "v1/nibog/getallusers/emails", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1220, 2760], "id": "413e86ca-09b3-4858-969b-b148a5a1d7b9", "name": "Get all users emails", "webhookId": "95a992ac-cd98-4609-88e6-18efcf94dc14"}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/get-event-registers-parents/emails", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1220, 2920], "id": "39671e6c-a126-40f0-9913-3fbfb06c0033", "name": "Get event registers parent emails", "webhookId": "95a992ac-cd98-4609-88e6-18efcf94dc14"}, {"parameters": {"path": "v1/nibog/get-all-parents/emails", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1220, 3100], "id": "ada1eb34-4fa7-4502-84f7-b40c9e11936e", "name": "Get all parents emails", "webhookId": "95a992ac-cd98-4609-88e6-18efcf94dc14"}, {"parameters": {"path": "v1/nibog/get-user-created-account/emails", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1220, 3280], "id": "600fcc76-63f3-4ba6-b459-efc576d9792c", "name": "get user created account emails", "webhookId": "95a992ac-cd98-4609-88e6-18efcf94dc14"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT email FROM users\nUNION\nSELECT email FROM parents;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1440, 2760], "id": "4332bf9e-d6fb-4f0a-a7b3-df0dacd6c244", "name": "get all users emails", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DISTINCT email\nFROM parents;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1440, 3100], "id": "b8f5d64c-54bf-4b4e-9308-a746bde6cdf2", "name": "get all parents emails", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DISTINCT email\nFROM users\nWHERE is_active = TRUE;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1440, 3280], "id": "f26b7460-4385-48a6-bf5b-847f2fab5b35", "name": "get created user account emails", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/city/get-all-city-event-count", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-400, 280], "id": "63d598ad-390d-42f7-a933-82d0a4c25ebb", "name": "get all city with city and event count", "webhookId": "aaf9d82a-4d16-4f77-9dc9-7d0a75e59c6d"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n    c.*,  -- All columns from the cities table\n    COUNT(DISTINCT v.id) AS venue_count,\n    COUNT(DISTINCT e.id) AS event_count\nFROM\n    cities c\nLEFT JOIN venues v ON v.city_id = c.id\nLEFT JOIN events e ON e.city_id = c.id\nGROUP BY\n    c.id\nORDER BY\n    c.city_name;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-300, 280], "id": "d4c13960-8098-4df2-a55a-a31249908742", "name": "Get All citys with event and venus count", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"path": "v1/nibog/venues/getall-with-city-event-count", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-220, 1140], "id": "692f17cc-44f8-44dd-80b3-810e4f3121aa", "name": "Get all venues with city event count", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n    venues.id AS venue_id,\n    venues.venue_name,\n    venues.address,\n    venues.capacity,\n    venues.is_active AS venue_is_active,\n    venues.created_at AS venue_created_at,\n    venues.updated_at AS venue_updated_at,\n    \n    cities.id AS city_id,\n    cities.city_name,\n    cities.state,\n    cities.is_active AS city_is_active,\n    cities.created_at AS city_created_at,\n    cities.updated_at AS city_updated_at,\n    \n    COUNT(events.id) AS event_count  -- Count of events per venue\n\nFROM \n    venues\nJOIN \n    cities ON venues.city_id = cities.id\nLEFT JOIN \n    events ON events.venue_id = venues.id\n\nWHERE \n    venues.is_active = TRUE \n    AND cities.is_active = TRUE\n\nGROUP BY \n    venues.id,\n    cities.id\n\nORDER BY \n    venues.venue_name;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-120, 1140], "id": "56ad94cf-47d3-4266-9f67-b21101c7a023", "name": "get all venues with city event count", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/venues/get-by-city-new", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-220, 1360], "id": "40106ee1-1aae-4452-a679-5f0642dd5bd9", "name": "Get venues by City ID1", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n    v.id,\n    v.venue_name,\n    v.address,\n    v.city_id,\n    v.capacity,\n    v.is_active,\n    v.created_at,\n    v.updated_at,\n    COUNT(e.id) AS event_count\nFROM venues v\nLEFT JOIN events e ON v.id = e.venue_id AND e.city_id = v.city_id\nWHERE v.city_id = {{ $json.body.city_id }}  -- Replace $1 with the desired city_id, or use a bound parameter in your app\nGROUP BY v.id\nORDER BY v.venue_name;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-120, 1360], "id": "d9341d19-f721-48d0-b6ad-81ed78456da5", "name": "Get venues by city Id1", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/get-upcoming-event/bycity/id", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-720, 640], "id": "7dc9607f-efd9-4b77-ba7e-2e5081cecaa2", "name": "get upcoming event by city id", "webhookId": "ecd0896a-05a5-4e8e-9f89-f6d5e71c144e"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  e.id AS event_id,\n  e.title,\n  e.description,\n  e.event_date,\n  e.status,\n  v.venue_name,\n  c.city_name,\n  c.state\nFROM \n  events e\nJOIN \n  cities c ON e.city_id = c.id\nJOIN \n  venues v ON e.venue_id = v.id\nWHERE \n  e.city_id = {{ $json.body.city_id }}\n  AND e.event_date >= CURRENT_DATE\nORDER BY \n  e.event_date ASC;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-620, 640], "id": "a67587c7-1c45-4239-8360-63b3e36d48fc", "name": "get upcoming event by cityid", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH event_stats AS (\n  SELECT\n    COUNT(*) AS total_events,\n    COUNT(*) FILTER (WHERE event_date >= CURRENT_DATE) AS upcoming_events,\n    COUNT(*) FILTER (WHERE event_date < CURRENT_DATE) AS past_events\n  FROM \n    events\n  WHERE \n    venue_id = {{ $json.body.venues_id }}\n),\nupcoming_events AS (\n  SELECT \n    e.id AS event_id,\n    e.title,\n    e.description,\n    e.event_date,\n    e.status,\n    v.venue_name,\n    v.address,\n    c.city_name,\n    c.state\n  FROM \n    events e\n  JOIN \n    venues v ON e.venue_id = v.id\n  JOIN \n    cities c ON e.city_id = c.id\n  WHERE \n    e.venue_id = {{ $json.body.venues_id }}\n    AND e.event_date >= CURRENT_DATE\n  ORDER BY \n    e.event_date ASC\n)\nSELECT \n  (SELECT row_to_json(event_stats) FROM event_stats) AS summary,\n  (SELECT json_agg(upcoming_events) FROM upcoming_events) AS upcoming_events;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-320, 1500], "id": "********-ad1f-49a6-81dd-d89ca754dc23", "name": "Get venues by city Id2", "alwaysOutputData": true, "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "v1/nibog/get-upcoming-events/venues-id", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-420, 1500], "id": "745d0e9e-0b86-46a7-bff9-28c24c5ed4cc", "name": "Get upcoming event by venue id", "webhookId": "c6d7046d-528b-45e3-99a1-c25de1918ec3"}, {"parameters": {"content": "## Dashboard api's\n**Double click** to edit me. [Guide](https://docs.n8n.io/workflows/sticky-notes/)", "height": 700, "width": 1020}, "type": "n8n-nodes-base.stickyNote", "position": [1500, 300], "typeVersion": 1, "id": "8534b2b2-3c6f-499e-949b-aff141e4805c", "name": "Sticky Note24"}, {"parameters": {"path": "v1/nibog/dashboard/api", "responseMode": "lastNode", "responseData": "allEntries", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1520, 400], "id": "0cda103f-c884-40b0-9638-b5531a89b0af", "name": "dashboard api", "webhookId": "8855679d-b061-46d4-bc5f-92210508dd86"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n  (SELECT COUNT(*) FROM baby_games) AS total_games,\n  (SELECT COUNT(*) FROM cities) AS total_cities,\n  (SELECT COUNT(*) FROM venues) AS total_venues,\n  (SELECT COUNT(*) FROM bookings) AS total_bookings;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [1620, 400], "id": "c810748f-140d-4eda-9430-0f42a73dce71", "name": "dashboard api1", "credentials": {"postgres": {"id": "mG9HxDfGNArdovBm", "name": "Postgres account"}}}], "pinData": {}, "connections": {"Role Create": {"main": [[{"node": "Role Table", "type": "main", "index": 0}]]}, "List Of roles": {"main": [[{"node": "Role-table-get-list", "type": "main", "index": 0}]]}, "Get One Role": {"main": [[{"node": "Role Table get single role", "type": "main", "index": 0}]]}, "Update a Role": {"main": [[{"node": "Role Table update single role", "type": "main", "index": 0}]]}, "Delete a Role": {"main": [[{"node": "Role Table1", "type": "main", "index": 0}]]}, "City add": {"main": [[{"node": "Add city", "type": "main", "index": 0}]]}, "update single city": {"main": [[{"node": "Update single city", "type": "main", "index": 0}]]}, "get all city": {"main": [[{"node": "Get All citys", "type": "main", "index": 0}]]}, "get single city by id": {"main": [[{"node": "Get single city", "type": "main", "index": 0}]]}, "single city delete": {"main": [[{"node": "Postgres", "type": "main", "index": 0}]]}, "create-baby-game": {"main": [[{"node": "add baby game", "type": "main", "index": 0}]]}, "get all baby games": {"main": [[{"node": "Get all baby games", "type": "main", "index": 0}]]}, "get single baby game": {"main": [[{"node": "get single baby game1", "type": "main", "index": 0}]]}, "Update single baby game": {"main": [[{"node": "update single baby game", "type": "main", "index": 0}]]}, "Delete single baby game": {"main": [[{"node": "Delete single baby game1", "type": "main", "index": 0}]]}, "Create venues": {"main": [[{"node": "create venues", "type": "main", "index": 0}]]}, "Get all venues": {"main": [[{"node": "get all venues", "type": "main", "index": 0}]]}, "Get venue by ID": {"main": [[{"node": "Get venue by Id", "type": "main", "index": 0}]]}, "Update venue by ID": {"main": [[{"node": "Update venue by Id", "type": "main", "index": 0}]]}, "Delete venue by ID": {"main": [[{"node": "Delete venue by Id", "type": "main", "index": 0}]]}, "Get venues by City ID": {"main": [[{"node": "Get venues by city Id", "type": "main", "index": 0}]]}, "Event create": {"main": [[{"node": "Event Create", "type": "main", "index": 0}]]}, "Get All Event": {"main": [[{"node": "Get All events", "type": "main", "index": 0}]]}, "Get Single Event": {"main": [[{"node": "Get single event", "type": "main", "index": 0}]]}, "Event delete": {"main": [[{"node": "Event Delete", "type": "main", "index": 0}]]}, "Event Update": {"main": [[{"node": "Event update", "type": "main", "index": 0}]]}, "Event with game slot created": {"main": [[{"node": "Event with game slot Create", "type": "main", "index": 0}]]}, "Get All Event with game slot": {"main": [[{"node": "Get All event with game slot", "type": "main", "index": 0}]]}, "Get Single Event with game slot": {"main": [[{"node": "Get single Event with game slot", "type": "main", "index": 0}]]}, "Event with game slot Update": {"main": [[{"node": "Event with game slot update", "type": "main", "index": 0}]]}, "Event with game slot delete": {"main": [[{"node": "Event with game slot Delete", "type": "main", "index": 0}]]}, "Employee create": {"main": [[{"node": "employee create", "type": "main", "index": 0}]]}, "get single employee": {"main": [[{"node": "Get single employee", "type": "main", "index": 0}]]}, "Get all employee": {"main": [[{"node": "get all employee", "type": "main", "index": 0}]]}, "Update employee": {"main": [[{"node": "update employee", "type": "main", "index": 0}]]}, "Delete single employee": {"main": [[{"node": "delete single employee", "type": "main", "index": 0}]]}, "Get all venues with city": {"main": [[{"node": "get all venues with city", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Postgres2", "type": "main", "index": 0}]]}, "Social media create": {"main": [[{"node": "social media", "type": "main", "index": 0}]]}, "Social media get": {"main": [[{"node": "social meda get", "type": "main", "index": 0}]]}, "Email setting create": {"main": [[{"node": "email setting create", "type": "main", "index": 0}]]}, "Email setting get": {"main": [[{"node": "email setting get", "type": "main", "index": 0}]]}, "General setting create": {"main": [[{"node": "general setting create", "type": "main", "index": 0}]]}, "General setting get": {"main": [[{"node": "general setting get", "type": "main", "index": 0}]]}, "event registration": {"main": [[{"node": "Event registration", "type": "main", "index": 0}]]}, "event rgistration get all": {"main": [[{"node": "event registration get all", "type": "main", "index": 0}]]}, "Event registration": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "event registration single get": {"main": [[{"node": "Event registration single get", "type": "main", "index": 0}]]}, "event registration del": {"main": [[{"node": "event register del", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Event registration update2", "type": "main", "index": 0}]]}, "event registration update": {"main": [[{"node": "Event registration update", "type": "main", "index": 0}]]}, "Event registration update": {"main": [[{"node": "del exisiting data", "type": "main", "index": 0}]]}, "del exisiting data": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "get event registration by city id": {"main": [[{"node": "event registration get all1", "type": "main", "index": 0}]]}, "user register": {"main": [[{"node": "registration", "type": "main", "index": 0}]]}, "user login": {"main": [[{"node": "User login", "type": "main", "index": 0}]]}, "User login": {"main": [[{"node": "Code2", "type": "main", "index": 0}]]}, "Code2": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "user list": {"main": [[{"node": "get users list", "type": "main", "index": 0}]]}, "user del": {"main": [[{"node": "del user", "type": "main", "index": 0}]]}, "user edit": {"main": [[{"node": "edit user", "type": "main", "index": 0}]]}, "bookings register": {"main": [[{"node": "Insert Parent", "type": "main", "index": 0}]]}, "get all bookings": {"main": [[{"node": "get all registration", "type": "main", "index": 0}]]}, "Code3": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "superadmin login": {"main": [[{"node": "superadmin employee login", "type": "main", "index": 0}]]}, "superadmin employee login": {"main": [[{"node": "Code3", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "true", "type": "main", "index": 0}], [{"node": "False", "type": "main", "index": 0}]]}, "Promo code created": {"main": [[{"node": "create promo code", "type": "main", "index": 0}]]}, "create promo code": {"main": [[{"node": "create promo", "type": "main", "index": 0}]]}, "create promo": {"main": [[{"node": "promo_code_mapping", "type": "main", "index": 0}]]}, "get all promocode": {"main": [[{"node": "get list of promo codes", "type": "main", "index": 0}]]}, "get single promo code": {"main": [[{"node": "get single item", "type": "main", "index": 0}]]}, "del the single item": {"main": [[{"node": "del promo code", "type": "main", "index": 0}]]}, "Code4": {"main": [[{"node": "Postgres4", "type": "main", "index": 0}]]}, "promocode update": {"main": [[{"node": "Update promocode", "type": "main", "index": 0}]]}, "Update promocode": {"main": [[{"node": "Delete mappings", "type": "main", "index": 0}]]}, "Delete mappings": {"main": [[{"node": "Code4", "type": "main", "index": 0}]]}, "Testimonials create": {"main": [[{"node": "Create testimonial", "type": "main", "index": 0}]]}, "Get single testimonial": {"main": [[{"node": "get testimonial by id", "type": "main", "index": 0}]]}, "Get-all testimonials": {"main": [[{"node": "Postgres1", "type": "main", "index": 0}]]}, "Testimonial update": {"main": [[{"node": "Testimonial update1", "type": "main", "index": 0}]]}, "Testimonial delete": {"main": [[{"node": "Delete testimonial", "type": "main", "index": 0}]]}, "Add-ons create": {"main": [[{"node": "Insert Add-on", "type": "main", "index": 0}]]}, "Insert Add-on": {"main": [[{"node": "Code5", "type": "main", "index": 0}]]}, "Code5": {"main": [[{"node": "Insert Images", "type": "main", "index": 0}]]}, "Insert Images": {"main": [[{"node": "Has<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "HasVariants": {"main": [[{"node": "Insert Variants", "type": "main", "index": 0}]]}, "Insert Variants": {"main": [[{"node": "Insert into Variants", "type": "main", "index": 0}]]}, "Get single Add-on": {"main": [[{"node": "Postgres5", "type": "main", "index": 0}]]}, "Get-all Add-ons": {"main": [[{"node": "Get-all", "type": "main", "index": 0}]]}, "Update": {"main": [[{"node": "Update Add-on", "type": "main", "index": 0}]]}, "Update Add-on": {"main": [[{"node": "Delete Add-on images", "type": "main", "index": 0}]]}, "Delete Add-on images": {"main": [[{"node": "Insert Add-on images", "type": "main", "index": 0}]]}, "Insert Add-on images": {"main": [[{"node": "Postgres8", "type": "main", "index": 0}]]}, "Delete variants": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Insert variants", "type": "main", "index": 0}]]}, "Delete Add-on": {"main": [[{"node": "Postgres3", "type": "main", "index": 0}]]}, "Payment-create": {"main": [[{"node": "Validate input", "type": "main", "index": 0}]]}, "Validate input": {"main": [[{"node": "Insert payments", "type": "main", "index": 0}]]}, "Insert payments": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Get all payments": {"main": [[{"node": "Get-all-payments", "type": "main", "index": 0}]]}, "Get-single-payment": {"main": [[{"node": "single-payment", "type": "main", "index": 0}]]}, "get-analytics": {"main": [[{"node": "get analytics", "type": "main", "index": 0}]]}, "Update status": {"main": [[{"node": "status-update", "type": "main", "index": 0}]]}, "Export": {"main": [[{"node": "Export payment list", "type": "main", "index": 0}]]}, "Export payment list": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Certificate-template": {"main": [[{"node": "Insert template", "type": "main", "index": 0}]]}, "Insert template": {"main": [[{"node": "Insert-template", "type": "main", "index": 0}]]}, "Get-all Templates": {"main": [[{"node": "Get-all1", "type": "main", "index": 0}]]}, "Get-single template": {"main": [[{"node": "Get-single", "type": "main", "index": 0}]]}, "Update Template": {"main": [[{"node": "Update-template", "type": "main", "index": 0}]]}, "Delete Template": {"main": [[{"node": "Delete-template", "type": "main", "index": 0}]]}, "Get by type": {"main": [[{"node": "return-by-type", "type": "main", "index": 0}]]}, "Generate-certificate": {"main": [[{"node": "validate", "type": "main", "index": 0}]]}, "validate": {"main": [[{"node": "Validate user", "type": "main", "index": 0}]]}, "Get Template": {"main": [[{"node": "Get Event", "type": "main", "index": 0}]]}, "Get Event": {"main": [[{"node": "Generate-Data", "type": "main", "index": 0}]]}, "Generate-Data": {"main": [[{"node": "Generate", "type": "main", "index": 0}]]}, "Download-pdf": {"main": [[{"node": "Extract-id", "type": "main", "index": 0}]]}, "Extract-id": {"main": [[{"node": "certificate-data", "type": "main", "index": 0}]]}, "certificate-data": {"main": [[{"node": "Generate HTML PDF", "type": "main", "index": 0}]]}, "Generate HTML PDF": {"main": [[]]}, "Update-status": {"main": [[{"node": "Bookingstatus-update", "type": "main", "index": 0}]]}, "Insert Parent": {"main": [[{"node": "Insert Child", "type": "main", "index": 0}]]}, "Insert Child": {"main": [[{"node": "Insert booking", "type": "main", "index": 0}]]}, "Insert booking": {"main": [[{"node": "Return games", "type": "main", "index": 0}]]}, "Return games": {"main": [[{"node": "booking games inserted", "type": "main", "index": 0}]]}, "booking games inserted": {"main": [[{"node": "Update max participants", "type": "main", "index": 0}]]}, "Get single booking": {"main": [[{"node": "Get booking-by-id", "type": "main", "index": 0}]]}, "Get by Event": {"main": [[{"node": "promocode by event", "type": "main", "index": 0}]]}, "get-by-event-games": {"main": [[{"node": "Return-available-scopes", "type": "main", "index": 0}]]}, "Validate": {"main": [[{"node": "Promocode Validate", "type": "main", "index": 0}]]}, "Promocode Validate": {"main": [[{"node": "Update usage count", "type": "main", "index": 0}]]}, "Update usage count": {"main": [[{"node": "Return message", "type": "main", "index": 0}]]}, "Return message": {"main": [[]]}, "Preview-Validation": {"main": [[{"node": "Promocode Preview", "type": "main", "index": 0}]]}, "Promocode Preview": {"main": [[{"node": "Return Preview", "type": "main", "index": 0}]]}, "Rollback-Api": {"main": [[{"node": "Reduce-usage count", "type": "main", "index": 0}]]}, "Validate user": {"main": [[{"node": "Get Template", "type": "main", "index": 0}]]}, "Get-all-certificates": {"main": [[{"node": "return certificates", "type": "main", "index": 0}]]}, "Get-single Certificate": {"main": [[{"node": "Return single certificate", "type": "main", "index": 0}]]}, "QRcode validate": {"main": [[{"node": "bookingdataattendance check", "type": "main", "index": 0}]]}, "bookingdataattendance check": {"main": [[{"node": "Code6", "type": "main", "index": 0}]]}, "Code6": {"main": [[{"node": "Switch2", "type": "main", "index": 0}]]}, "Switch2": {"main": [[{"node": "Code9", "type": "main", "index": 0}], [{"node": "Code8", "type": "main", "index": 0}], [{"node": "Code7", "type": "main", "index": 0}]]}, "Code8": {"main": [[{"node": "Postgres7", "type": "main", "index": 0}]]}, "Postgres7": {"main": [[{"node": "Code10", "type": "main", "index": 0}]]}, "Upcoming-events": {"main": [[{"node": "Upcoming-events1", "type": "main", "index": 0}]]}, "Upcomingevents-bycity": {"main": [[{"node": "get-upcoming-events", "type": "main", "index": 0}]]}, "get-upcoming-events": {"main": [[]]}, "Get-games-basedon-age": {"main": [[{"node": "gamesbyage", "type": "main", "index": 0}]]}, "Pending-bookings create": {"main": [[{"node": "Insert pending", "type": "main", "index": 0}]]}, "Get-pending data": {"main": [[{"node": "Get-data", "type": "main", "index": 0}]]}, "Delete-pendingdata": {"main": [[{"node": "Delete pending", "type": "main", "index": 0}]]}, "Insert addons": {"main": [[{"node": "Addons inserted", "type": "main", "index": 0}]]}, "Addons inserted": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "Promocode usage update": {"main": [[{"node": "Promcode updae", "type": "main", "index": 0}]]}, "Promcode updae": {"main": [[{"node": "webhook response", "type": "main", "index": 0}]]}, "Update max participants": {"main": [[{"node": "execute update max", "type": "main", "index": 0}]]}, "execute update max": {"main": [[{"node": "has Addons", "type": "main", "index": 0}]]}, "has Addons": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Insert addons", "type": "main", "index": 0}], [{"node": "If3", "type": "main", "index": 0}]]}, "Promocode usage update1": {"main": [[{"node": "Promcode updae1", "type": "main", "index": 0}]]}, "Promcode updae1": {"main": [[{"node": "webhook response", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "Promcode updae1", "type": "main", "index": 0}], [{"node": "webhook response", "type": "main", "index": 0}]]}, "Participants": {"main": [[{"node": "Extract id", "type": "main", "index": 0}]]}, "Extract id": {"main": [[{"node": "Get event details", "type": "main", "index": 0}]]}, "Get event details": {"main": [[{"node": "Get participants", "type": "main", "index": 0}]]}, "Get participants": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}, "completed events": {"main": [[{"node": "completedevents", "type": "main", "index": 0}]]}, "Postgres8": {"main": [[{"node": "Delete variants", "type": "main", "index": 0}]]}, "Insert variants": {"main": [[{"node": "Postgres9", "type": "main", "index": 0}]]}, "User bookings": {"main": [[{"node": "Postgres10", "type": "main", "index": 0}]]}, "Get booking_ref details": {"main": [[{"node": "Postgres6", "type": "main", "index": 0}]]}, "event registration single get1": {"main": [[{"node": "Event registration single get1", "type": "main", "index": 0}]]}, "Get-games-basedon-age1": {"main": [[{"node": "gamesbyage1", "type": "main", "index": 0}]]}, "Homepage sections created": {"main": [[{"node": "creating homesection", "type": "main", "index": 0}]]}, "Home section Get": {"main": [[{"node": "homesection get", "type": "main", "index": 0}]]}, "home section del": {"main": [[{"node": "home section delete", "type": "main", "index": 0}]]}, "Get all users emails": {"main": [[{"node": "get all users emails", "type": "main", "index": 0}]]}, "Get all parents emails": {"main": [[{"node": "get all parents emails", "type": "main", "index": 0}]]}, "get user created account emails": {"main": [[{"node": "get created user account emails", "type": "main", "index": 0}]]}, "get all city with city and event count": {"main": [[{"node": "Get All citys with event and venus count", "type": "main", "index": 0}]]}, "Get all venues with city event count": {"main": [[{"node": "get all venues with city event count", "type": "main", "index": 0}]]}, "Get venues by City ID1": {"main": [[{"node": "Get venues by city Id1", "type": "main", "index": 0}]]}, "get upcoming event by city id": {"main": [[{"node": "get upcoming event by cityid", "type": "main", "index": 0}]]}, "Get upcoming event by venue id": {"main": [[{"node": "Get venues by city Id2", "type": "main", "index": 0}]]}, "dashboard api": {"main": [[{"node": "dashboard api1", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "0f49ca39-0af1-4818-bd02-7d128291e166", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b2d0c076c6ac59adb2d279b3223017fa561fa8b87de80773612375246b4deead"}, "id": "LJaakyqJ1y0is18A", "tags": []}